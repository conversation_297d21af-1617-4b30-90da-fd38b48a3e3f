import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
import pandas as pd
import json
import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from transformers import pipeline
import tiktoken

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DocumentChunk:
    """Structured representation of a document chunk"""
    chunk_id: str
    title: str
    content: str
    page_range: str
    section_hierarchy: List[str]
    chunk_type: str  # 'text', 'table', 'list', 'heading'
    metadata: Dict
    token_count: int
    semantic_summary: str

class AdvancedPDFChunker:
    """
    State-of-the-art PDF chunking system for technical manuals
    Handles complex layouts, cross-page elements, and semantic boundaries
    """
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """Initialize the chunker with embedding model for semantic analysis"""
        self.embedding_model = SentenceTransformer(model_name)
        self.summarizer = pipeline("summarization", model="facebook/bart-large-cnn")
        self.encoding = tiktoken.get_encoding("cl100k_base")
        
        # Configuration for technical manuals
        self.config = {
            'min_chunk_size': 200,      # Minimum tokens
            'max_chunk_size': 1000,     # Maximum tokens
            'overlap_size': 50,         # Token overlap between chunks
            'semantic_threshold': 0.7,   # Similarity threshold for merging
            'heading_patterns': [
                r'^(\d+\.?\d*\.?\d*)\s+([A-Z][^\.]+)',  # Numbered headings
                r'^([A-Z][A-Z\s]+)$',                    # ALL CAPS headings
                r'^([A-Z][a-z\s]+):',                    # Title case with colon
            ],
            'table_indicators': ['table', 'figure', 'chart', 'diagram'],
            'cross_reference_patterns': [
                r'(see|refer to|as shown in)\s+(section|page|figure|table)\s+(\d+)',
                r'(section|chapter)\s+(\d+\.?\d*)',
            ]
        }
    
    def extract_text_with_layout(self, pdf_path: str) -> Dict:
        """Extract text while preserving layout information"""
        logger.info(f"Extracting text from {pdf_path}")
        
        document_data = {
            'pages': [],
            'metadata': {},
            'structure': []
        }
        
        # Use pdfplumber for layout-aware extraction
        with pdfplumber.open(pdf_path) as pdf:
            document_data['metadata'] = {
                'total_pages': len(pdf.pages),
                'title': pdf.metadata.get('Title', 'Unknown'),
                'author': pdf.metadata.get('Author', 'Unknown')
            }
            
            for page_num, page in enumerate(pdf.pages, 1):
                page_data = self._extract_page_content(page, page_num)
                document_data['pages'].append(page_data)
        
        return document_data
    
    def _extract_page_content(self, page, page_num: int) -> Dict:
        """Extract content from a single page with structure preservation"""
        page_data = {
            'page_number': page_num,
            'text_blocks': [],
            'tables': [],
            'images': [],
            'layout_info': {}
        }
        
        # Extract text with bounding boxes
        try:
            # Get text with layout
            text = page.extract_text()
            if text:
                lines = text.split('\n')
                for i, line in enumerate(lines):
                    if line.strip():
                        page_data['text_blocks'].append({
                            'text': line.strip(),
                            'line_number': i,
                            'type': self._classify_text_type(line.strip())
                        })
            
            # Extract tables
            tables = page.extract_tables()
            for i, table in enumerate(tables):
                if table:
                    page_data['tables'].append({
                        'table_id': f"table_{page_num}_{i}",
                        'data': table,
                        'markdown': self._table_to_markdown(table)
                    })
            
            # Get image information (we'll ignore content but note presence)
            images = page.images
            page_data['images'] = [{'image_id': f"img_{page_num}_{i}"} for i in range(len(images))]
            
        except Exception as e:
            logger.warning(f"Error extracting from page {page_num}: {e}")
        
        return page_data
    
    def _classify_text_type(self, text: str) -> str:
        """Classify text type for better chunking decisions"""
        text_lower = text.lower()
        
        # Check for headings
        for pattern in self.config['heading_patterns']:
            if re.match(pattern, text):
                return 'heading'
        
        # Check for table/figure references
        if any(indicator in text_lower for indicator in self.config['table_indicators']):
            return 'table_reference'
        
        # Check for list items
        if re.match(r'^\s*[-•*]\s+', text) or re.match(r'^\s*\d+[\.)]\s+', text):
            return 'list_item'
        
        # Check for cross-references
        for pattern in self.config['cross_reference_patterns']:
            if re.search(pattern, text_lower):
                return 'cross_reference'
        
        return 'paragraph'
    
    def _table_to_markdown(self, table: List[List]) -> str:
        """Convert table to markdown format for LLM compatibility"""
        if not table or not table[0]:
            return ""
        
        markdown_lines = []
        
        # Header row
        header = [str(cell) if cell else "" for cell in table[0]]
        markdown_lines.append("| " + " | ".join(header) + " |")
        markdown_lines.append("| " + " | ".join(["---"] * len(header)) + " |")
        
        # Data rows
        for row in table[1:]:
            if row:
                formatted_row = [str(cell) if cell else "" for cell in row]
                # Pad row to match header length
                while len(formatted_row) < len(header):
                    formatted_row.append("")
                markdown_lines.append("| " + " | ".join(formatted_row) + " |")
        
        return "\n".join(markdown_lines)
    
    def merge_cross_page_elements(self, document_data: Dict) -> Dict:
        """Merge elements that span across pages"""
        logger.info("Merging cross-page elements")
        
        merged_pages = []
        
        for i, page in enumerate(document_data['pages']):
            current_page = page.copy()
            
            # Check if last text block continues to next page
            if i < len(document_data['pages']) - 1:
                next_page = document_data['pages'][i + 1]
                
                # Merge incomplete sentences/paragraphs
                if (current_page['text_blocks'] and 
                    next_page['text_blocks'] and
                    not current_page['text_blocks'][-1]['text'].endswith(('.', '!', '?', ':'))):
                    
                    # Check if next page starts with lowercase (continuation)
                    next_first_text = next_page['text_blocks'][0]['text']
                    if next_first_text and next_first_text[0].islower():
                        # Merge the blocks
                        merged_text = (current_page['text_blocks'][-1]['text'] + " " + 
                                     next_first_text)
                        current_page['text_blocks'][-1]['text'] = merged_text
                        current_page['text_blocks'][-1]['type'] = 'merged_paragraph'
                        
                        # Mark for removal from next page
                        next_page['text_blocks'][0]['_remove'] = True
            
            merged_pages.append(current_page)
        
        # Clean up marked elements
        for page in merged_pages:
            page['text_blocks'] = [block for block in page['text_blocks'] 
                                 if not block.get('_remove', False)]
        
        document_data['pages'] = merged_pages
        return document_data
    
    def create_semantic_chunks(self, document_data: Dict) -> List[DocumentChunk]:
        """Create semantically meaningful chunks from document data"""
        logger.info("Creating semantic chunks")
        
        chunks = []
        current_section_hierarchy = []
        chunk_counter = 0
        
        for page in document_data['pages']:
            page_chunks = self._process_page_for_chunks(
                page, current_section_hierarchy, chunk_counter
            )
            chunks.extend(page_chunks)
            chunk_counter += len(page_chunks)
        
        # Post-process chunks for optimization
        optimized_chunks = self._optimize_chunks(chunks)
        
        return optimized_chunks
    
    def _process_page_for_chunks(self, page: Dict, section_hierarchy: List[str], 
                               start_counter: int) -> List[DocumentChunk]:
        """Process a single page to create chunks"""
        chunks = []
        current_chunk_content = []
        current_chunk_type = 'text'
        chunk_counter = start_counter
        
        # Process text blocks
        for block in page['text_blocks']:
            if block['type'] == 'heading':
                # Finalize current chunk if exists
                if current_chunk_content:
                    chunk = self._create_chunk_from_content(
                        current_chunk_content, current_chunk_type, 
                        chunk_counter, page['page_number'], section_hierarchy
                    )
                    chunks.append(chunk)
                    current_chunk_content = []
                    chunk_counter += 1
                
                # Update section hierarchy
                heading_text = block['text']
                level = self._determine_heading_level(heading_text)
                
                # Adjust hierarchy
                if level <= len(section_hierarchy):
                    section_hierarchy = section_hierarchy[:level-1]
                section_hierarchy.append(heading_text)
                
                # Start new chunk with heading
                current_chunk_content = [block['text']]
                current_chunk_type = 'section'
            
            elif block['type'] in ['paragraph', 'merged_paragraph']:
                current_chunk_content.append(block['text'])
                
                # Check if chunk is getting too large
                if self._calculate_token_count('\n'.join(current_chunk_content)) > self.config['max_chunk_size']:
                    chunk = self._create_chunk_from_content(
                        current_chunk_content[:-1], current_chunk_type,
                        chunk_counter, page['page_number'], section_hierarchy
                    )
                    chunks.append(chunk)
                    
                    # Start new chunk with overlap
                    overlap_content = self._create_overlap(current_chunk_content[-1])
                    current_chunk_content = overlap_content
                    chunk_counter += 1
        
        # Process tables separately
        for table in page['tables']:
            if current_chunk_content:
                # Finalize text chunk
                chunk = self._create_chunk_from_content(
                    current_chunk_content, current_chunk_type,
                    chunk_counter, page['page_number'], section_hierarchy
                )
                chunks.append(chunk)
                current_chunk_content = []
                chunk_counter += 1
            
            # Create table chunk
            table_chunk = self._create_table_chunk(
                table, chunk_counter, page['page_number'], section_hierarchy
            )
            chunks.append(table_chunk)
            chunk_counter += 1
        
        # Finalize remaining content
        if current_chunk_content:
            chunk = self._create_chunk_from_content(
                current_chunk_content, current_chunk_type,
                chunk_counter, page['page_number'], section_hierarchy
            )
            chunks.append(chunk)
        
        return chunks
    
    def _create_chunk_from_content(self, content: List[str], chunk_type: str,
                                 chunk_id: int, page_num: int, 
                                 section_hierarchy: List[str]) -> DocumentChunk:
        """Create a DocumentChunk from content"""
        full_content = '\n'.join(content)
        token_count = self._calculate_token_count(full_content)
        
        # Generate descriptive title
        title = self._generate_chunk_title(content, section_hierarchy)
        
        # Generate semantic summary
        summary = self._generate_semantic_summary(full_content)
        
        return DocumentChunk(
            chunk_id=f"chunk_{chunk_id:04d}",
            title=title,
            content=full_content,
            page_range=str(page_num),
            section_hierarchy=section_hierarchy.copy(),
            chunk_type=chunk_type,
            metadata={
                'page_number': page_num,
                'section_path': ' > '.join(section_hierarchy),
                'content_lines': len(content)
            },
            token_count=token_count,
            semantic_summary=summary
        )
    
    def _create_table_chunk(self, table: Dict, chunk_id: int, page_num: int,
                          section_hierarchy: List[str]) -> DocumentChunk:
        """Create a chunk specifically for tables"""
        content = f"Table ID: {table['table_id']}\n\n{table['markdown']}"
        token_count = self._calculate_token_count(content)
        
        title = f"Table from {' > '.join(section_hierarchy[-2:]) if len(section_hierarchy) >= 2 else 'Document'}"
        summary = f"Tabular data with {len(table['data'])} rows"
        
        return DocumentChunk(
            chunk_id=f"chunk_{chunk_id:04d}",
            title=title,
            content=content,
            page_range=str(page_num),
            section_hierarchy=section_hierarchy.copy(),
            chunk_type='table',
            metadata={
                'page_number': page_num,
                'table_id': table['table_id'],
                'table_rows': len(table['data']),
                'table_cols': len(table['data'][0]) if table['data'] else 0
            },
            token_count=token_count,
            semantic_summary=summary
        )
    
    def _generate_chunk_title(self, content: List[str], section_hierarchy: List[str]) -> str:
        """Generate descriptive title for chunk"""
        if not content:
            return "Empty Chunk"
        
        # If we have section hierarchy, use it
        if section_hierarchy:
            base_title = section_hierarchy[-1]
            # If chunk starts with different content, modify title
            first_line = content[0].strip()
            if first_line and first_line != base_title:
                # Extract key terms from first line
                key_terms = self._extract_key_terms(first_line)
                if key_terms:
                    return f"{base_title}: {key_terms}"
            return base_title
        
        # Extract key terms from content
        full_text = ' '.join(content[:3])  # Use first few lines
        key_terms = self._extract_key_terms(full_text)
        return key_terms if key_terms else "Document Content"
    
    def _extract_key_terms(self, text: str, max_length: int = 60) -> str:
        """Extract key terms from text for title generation"""
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        
        words = re.findall(r'\b[A-Za-z]{3,}\b', text.lower())
        meaningful_words = [w for w in words if w not in stop_words]
        
        # Take first few meaningful words
        title_words = meaningful_words[:5]
        title = ' '.join(title_words).title()
        
        return title[:max_length] if len(title) > max_length else title
    
    def _generate_semantic_summary(self, content: str) -> str:
        """Generate semantic summary of chunk content"""
        if len(content) < 100:
            return content[:100] + "..." if len(content) > 100 else content
        
        try:
            # Use summarizer for longer content
            if len(content) > 500:
                summary = self.summarizer(content, max_length=100, min_length=30, do_sample=False)
                return summary[0]['summary_text']
            else:
                # For shorter content, return first sentence or two
                sentences = re.split(r'[.!?]+', content)
                return '. '.join(sentences[:2]).strip() + '.'
        except Exception as e:
            logger.warning(f"Error generating summary: {e}")
            return content[:150] + "..."
    
    def _determine_heading_level(self, heading: str) -> int:
        """Determine heading level for hierarchy"""
        # Check for numbered headings (1., 1.1., 1.1.1.)
        match = re.match(r'^(\d+)(\.\d+)*', heading)
        if match:
            return len(match.group(0).split('.'))
        
        # Check for formatting cues
        if heading.isupper():
            return 1
        elif ':' in heading:
            return 2
        else:
            return 3
    
    def _calculate_token_count(self, text: str) -> int:
        """Calculate token count for text"""
        return len(self.encoding.encode(text))
    
    def _create_overlap(self, text: str) -> List[str]:
        """Create overlap content for chunk boundaries"""
        sentences = re.split(r'[.!?]+', text)
        # Return last sentence as overlap
        return [sentences[-1].strip()] if sentences and sentences[-1].strip() else []
    
    def _optimize_chunks(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Optimize chunks by merging small ones and splitting large ones"""
        optimized = []
        
        i = 0
        while i < len(chunks):
            current_chunk = chunks[i]
            
            # If chunk is too small, try to merge with next
            if (current_chunk.token_count < self.config['min_chunk_size'] and 
                i + 1 < len(chunks) and
                chunks[i + 1].chunk_type == current_chunk.chunk_type):
                
                next_chunk = chunks[i + 1]
                merged_content = current_chunk.content + "\n\n" + next_chunk.content
                merged_tokens = self._calculate_token_count(merged_content)
                
                if merged_tokens <= self.config['max_chunk_size']:
                    # Merge chunks
                    merged_chunk = DocumentChunk(
                        chunk_id=current_chunk.chunk_id,
                        title=current_chunk.title,
                        content=merged_content,
                        page_range=f"{current_chunk.page_range}-{next_chunk.page_range}",
                        section_hierarchy=current_chunk.section_hierarchy,
                        chunk_type=current_chunk.chunk_type,
                        metadata={**current_chunk.metadata, 'merged': True},
                        token_count=merged_tokens,
                        semantic_summary=self._generate_semantic_summary(merged_content)
                    )
                    optimized.append(merged_chunk)
                    i += 2  # Skip next chunk as it's merged
                    continue
            
            optimized.append(current_chunk)
            i += 1
        
        return optimized
    
    def export_chunks(self, chunks: List[DocumentChunk], output_format: str = 'json') -> str:
        """Export chunks in specified format"""
        if output_format.lower() == 'json':
            return json.dumps([asdict(chunk) for chunk in chunks], indent=2, ensure_ascii=False)
        
        elif output_format.lower() == 'csv':
            df = pd.DataFrame([asdict(chunk) for chunk in chunks])
            return df.to_csv(index=False)
        
        elif output_format.lower() == 'jsonl':
            return '\n'.join([json.dumps(asdict(chunk), ensure_ascii=False) for chunk in chunks])
        
        else:
            raise ValueError(f"Unsupported format: {output_format}")
    
    def process_pdf(self, pdf_path: str, output_format: str = 'json') -> str:
        """Main method to process PDF and return chunks"""
        logger.info(f"Starting PDF processing for {pdf_path}")
        
        # Step 1: Extract text with layout preservation
        document_data = self.extract_text_with_layout(pdf_path)
        
        # Step 2: Merge cross-page elements
        document_data = self.merge_cross_page_elements(document_data)
        
        # Step 3: Create semantic chunks
        chunks = self.create_semantic_chunks(document_data)
        
        # Step 4: Export in requested format
        result = self.export_chunks(chunks, output_format)
        
        logger.info(f"Processing complete. Generated {len(chunks)} chunks")
        return result

# Usage example and utility functions
def main():
    """Example usage of the PDF chunker"""
    chunker = AdvancedPDFChunker()
    
    # Process PDF
    pdf_path = "mac.pdf"  # Replace with your PDF path
    
    try:
        # Generate chunks in JSON format
        result = chunker.process_pdf(pdf_path, output_format='json')
        
        # Save to file
        output_path = Path(pdf_path).stem + "_chunks.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(result)
        
        print(f"Chunks saved to {output_path}")
        
        # Also create a summary report
        chunks_data = json.loads(result)
        create_summary_report(chunks_data, Path(pdf_path).stem + "_summary.txt")
        
    except Exception as e:
        logger.error(f"Error processing PDF: {e}")

def create_summary_report(chunks_data: List[Dict], report_path: str):
    """Create a summary report of the chunking results"""
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("PDF CHUNKING SUMMARY REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Total Chunks: {len(chunks_data)}\n")
        
        # Chunk type distribution
        chunk_types = {}
        total_tokens = 0
        
        for chunk in chunks_data:
            chunk_type = chunk['chunk_type']
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
            total_tokens += chunk['token_count']
        
        f.write(f"Total Tokens: {total_tokens}\n")
        f.write(f"Average Tokens per Chunk: {total_tokens / len(chunks_data):.1f}\n\n")
        
        f.write("Chunk Type Distribution:\n")
        for chunk_type, count in chunk_types.items():
            f.write(f"  {chunk_type}: {count}\n")
        
        f.write("\nSample Chunk Titles:\n")
        for i, chunk in enumerate(chunks_data[:10]):
            f.write(f"  {i+1}. {chunk['title']}\n")

if __name__ == "__main__":
    main()