{"chunk_id": "text_18_0", "content_type": "text", "text_content": "Example 1: Three program knobs are pushed down and one turn of main pipe takes 60 sec  (measured with a wrist watch by checking time for one turn of the lifting rod). How long time  does it take to wash the tank (one full cycle)?", "structured_data": {"section": "Calculation of cleaning time"}, "metadata": {"page_number": 18, "section": "Calculation of cleaning time", "content_type": "general", "criticality": "medium", "entities": {}, "keywords": ["turn", "time", "example", "three", "program", "knobs", "pushed", "down", "main", "pipe"], "word_count": 45}, "relationships": ["table_18_0"]}