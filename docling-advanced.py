from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
import pandas as pd

converter = DocumentConverter()

# Convert with specific options
result = converter.convert(
    "mac.pdf"
)

# Access different content types
document = result.document

# # Get tables
# for table in document.tables:
#     print(f"Table: {table.export_to_text()}")

# Get images
# for image in document.pictures:
#     print(image.captions)

# Get text with formatting preserved
# markdown_content = document.export_to_markdown()
# print(markdown_content)

for table_ix, table in enumerate(document.tables):
        print(f"Table {table_ix}: {table.export_to_markdown()}")
        # table_df: pd.DataFrame = table.export_to_dataframe()
        # print(f"## Table {table_ix}")
        # print(table_df.to_markdown())
