import fitz  # PyMuPDF
import pdfplumber
import pandas as pd
import re
from typing import Dict, List, Tuple, Any
import json
from dataclasses import dataclass
from enum import Enum
import os
from datetime import datetime
from pathlib import Path

# =============================================================================
# 1. TEXT EXTRACTION - Convert narrative text while preserving context
# =============================================================================

class TextExtractor:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.doc = fitz.open(pdf_path)
        self.header_footer_patterns = set()  # Store detected header/footer patterns
    
    def extract_text_with_context(self) -> List[Dict]:
        """Extract text while preserving document structure and context"""
        pages_data = []
        
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            
            # Extract text blocks with formatting info
            blocks = page.get_text("dict")
            
            page_data = {
                "page_number": page_num + 1,
                "sections": self._identify_sections(blocks),
                "paragraphs": self._extract_paragraphs(blocks),
                "headers": self._extract_headers(blocks)
            }
            
            pages_data.append(page_data)
        
        return pages_data
    
    def _identify_sections(self, blocks: Dict) -> List[Dict]:
        """Identify section headers and their hierarchy"""
        sections = []
        
        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        font_size = span["size"]
                        font_flags = span["flags"]  # Bold, italic, etc.
                        
                        # Detect section headers based on formatting
                        if self._is_section_header(text, font_size, font_flags):
                            sections.append({
                                "text": text,
                                "level": self._get_header_level(font_size, font_flags),
                                "bbox": span["bbox"],
                                "section_number": self._extract_section_number(text)
                            })
        
        return sections
    
    def _is_section_header(self, text: str, font_size: float, font_flags: int) -> bool:
        """Determine if text is a section header"""
        # Check for bold text (flag 16)
        is_bold = font_flags & 2**4
        
        # Check for section patterns
        section_patterns = [
            r'^\d+\.\d*\s*-?\s*[A-Z]',  # "3.2 - Component Details"
            r'^Section\s+\d+',           # "Section 3"
            r'^Chapter\s+\d+',           # "Chapter 3"
            r'^[A-Z\s]{5,}$'             # ALL CAPS headers
        ]
        
        has_section_pattern = any(re.match(pattern, text) for pattern in section_patterns)
        
        return (is_bold and font_size > 12) or has_section_pattern
    
    def _get_header_level(self, font_size: float, font_flags: int) -> int:
        """Determine header hierarchy level"""
        if font_size >= 16:
            return 1  # Main section
        elif font_size >= 14:
            return 2  # Subsection
        else:
            return 3  # Sub-subsection
    
    def _extract_section_number(self, text: str) -> str:
        """Extract section number from header text"""
        match = re.match(r'(\d+\.?\d*)', text)
        return match.group(1) if match else ""
    
    def _extract_paragraphs(self, blocks: Dict) -> List[Dict]:
        """Extract paragraphs with their context, filtering out headers/footers"""
        # First detect header/footer patterns if not already done
        if not self.header_footer_patterns:
            self._detect_header_footer_patterns()

        paragraphs = []
        current_section = "unknown"

        for block in blocks["blocks"]:
            if "lines" in block:
                paragraph_text = ""
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]
                    paragraph_text += line_text + " "

                paragraph_text = paragraph_text.strip()

                if paragraph_text:
                    # Skip if it's a header/footer
                    if self._is_header_footer(paragraph_text):
                        continue

                    # Skip if it's a section header
                    if self._is_section_header(paragraph_text, 12, 0):
                        continue

                    paragraphs.append({
                        "text": paragraph_text,
                        "section": current_section,
                        "bbox": block["bbox"]
                    })

        return paragraphs
    
    def _extract_headers(self, blocks: Dict) -> List[str]:
        """Extract all headers for context"""
        headers = []
        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if self._is_section_header(text, span["size"], span["flags"]):
                            headers.append(text)
        return headers

    def _detect_header_footer_patterns(self) -> None:
        """Analyze all pages to detect repeating header/footer patterns"""
        text_blocks_by_position = {}

        for page_num in range(min(10, len(self.doc))):  # Sample first 10 pages
            page = self.doc[page_num]
            blocks = page.get_text("dict")["blocks"]

            for block in blocks:
                if "lines" in block:
                    text_content = ""
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text_content += span["text"]

                    if text_content.strip():
                        # Normalize text for comparison
                        normalized_text = self._normalize_text_for_comparison(text_content.strip())

                        # Use position to group similar blocks
                        bbox = block["bbox"]
                        y_position = round(bbox[1] / 50) * 50  # Group by approximate Y position

                        position_key = f"y_{y_position}"

                        if position_key not in text_blocks_by_position:
                            text_blocks_by_position[position_key] = {}

                        if normalized_text not in text_blocks_by_position[position_key]:
                            text_blocks_by_position[position_key][normalized_text] = 0

                        text_blocks_by_position[position_key][normalized_text] += 1

        # Identify patterns that appear on multiple pages
        for position, texts in text_blocks_by_position.items():
            for text, count in texts.items():
                if count >= 3:  # Appears on 3+ pages
                    self.header_footer_patterns.add(text)

    def _normalize_text_for_comparison(self, text: str) -> str:
        """Normalize text for header/footer pattern comparison"""
        # Remove extra whitespace and normalize
        normalized = re.sub(r'\s+', ' ', text.strip())

        # Check if it matches known footer pattern
        footer_patterns = [
            r'Södra Långebergsgatan.*Sweden',
            r'P\.O\. Box.*Göteborg',
            r'Telephone.*Telefax.*Email.*Web',
            r'office@scanjet\.se.*www\.scanjet\.se'
        ]

        for pattern in footer_patterns:
            if re.search(pattern, normalized, re.IGNORECASE):
                return "SCANJET_FOOTER"

        return normalized

    def _is_header_footer(self, text: str) -> bool:
        """Check if text is a header or footer that should be ignored"""
        normalized = self._normalize_text_for_comparison(text)

        # Check against detected patterns
        if normalized in self.header_footer_patterns:
            return True

        # Check for common header/footer indicators
        header_footer_indicators = [
            "SCANJET_FOOTER",
            "page",
            "confidential",
            "proprietary",
            "copyright",
            "all rights reserved"
        ]

        text_lower = text.lower()
        for indicator in header_footer_indicators:
            if indicator in text_lower:
                return True

        # Check if it's the Scanjet contact info footer
        scanjet_footer_parts = [
            "södra långebergsgatan",
            "p.o. box 9316",
            "se-400 97 göteborg",
            "<EMAIL>",
            "www.scanjet.se"
        ]

        text_lower = text.lower()
        matching_parts = sum(1 for part in scanjet_footer_parts if part in text_lower)

        # If it contains 2 or more parts of the footer, consider it a footer
        if matching_parts >= 2:
            return True

        return False

# =============================================================================
# 2. TABLE CONVERSION - Transform tables to natural language + structured JSON
# =============================================================================

class TableConverter:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
    
    def extract_tables_with_context(self) -> List[Dict]:
        """Extract tables and convert to both formats"""
        tables_data = []
        
        with pdfplumber.open(self.pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                tables = page.extract_tables()
                
                for table_idx, table in enumerate(tables):
                    if table and len(table) > 1:  # Has header + data
                        table_data = self._process_table(table, page_num + 1, table_idx)
                        if table_data:  # Only append if processing was successful
                            tables_data.append(table_data)
        
        return tables_data
    
    def _process_table(self, table: List[List], page_num: int, table_idx: int) -> Dict:
        """Process a single table into both formats"""
        if not table or len(table) < 1:
            return None

        headers = table[0]  # First row as headers
        rows = table[1:]    # Data rows

        # Clean headers
        clean_headers = [self._clean_text(h) for h in headers if h]

        # If no valid headers, create generic column names
        if not clean_headers:
            if not rows or not rows[0]:
                return None
            num_cols = len(rows[0]) if rows else 0
            clean_headers = [f"Column_{i+1}" for i in range(num_cols)]

        # Ensure all rows have the same number of columns as headers
        max_cols = len(clean_headers)
        normalized_rows = []
        for row in rows:
            if row:  # Skip empty rows
                # Pad or truncate row to match header count
                normalized_row = (row + [None] * max_cols)[:max_cols]
                normalized_rows.append(normalized_row)

        if not normalized_rows:
            return None

        # Convert to DataFrame for easier processing
        try:
            df = pd.DataFrame(normalized_rows, columns=clean_headers)
            df = df.dropna(how='all').fillna('')  # Remove empty rows, fill NaN
        except Exception as e:
            print(f"Error creating DataFrame for table on page {page_num}: {e}")
            return None
        
        # Generate natural language description
        natural_lang = self._table_to_natural_language(df, clean_headers)
        
        # Create structured JSON
        structured_data = self._table_to_structured_json(df, clean_headers)
        
        # Identify table type and context
        table_type = self._identify_table_type(clean_headers, df)
        
        return {
            "page_number": page_num,
            "table_index": table_idx,
            "table_type": table_type,
            "headers": clean_headers,
            "natural_language": natural_lang,
            "structured_data": structured_data,
            "row_count": len(df),
            "column_count": len(clean_headers)
        }
    
    def _clean_text(self, text: str) -> str:
        """Clean extracted text"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text.strip())
    
    def _table_to_natural_language(self, df: pd.DataFrame, headers: List[str]) -> str:
        """Convert table to natural language description"""
        descriptions = []
        
        for _, row in df.iterrows():
            row_desc = []
            for header in headers:
                if header in row and row[header]:
                    row_desc.append(f"{header}: {row[header]}")
            
            if row_desc:
                descriptions.append(", ".join(row_desc))
        
        return ". ".join(descriptions) + "."
    
    def _table_to_structured_json(self, df: pd.DataFrame, headers: List[str]) -> Dict:
        """Convert table to structured JSON"""
        return {
            "headers": headers,
            "rows": df.to_dict('records'),
            "summary": {
                "total_rows": len(df),
                "columns": headers
            }
        }
    
    def _identify_table_type(self, headers: List[str], df: pd.DataFrame) -> str:
        """Identify the type of table based on headers and content"""
        header_text = " ".join(headers).lower()
        
        # Define table type patterns
        type_patterns = {
            "component_specs": ["part", "component", "specification", "model"],
            "maintenance_schedule": ["maintenance", "schedule", "interval", "frequency"],
            "spare_parts": ["spare", "part", "inventory", "stock"],
            "troubleshooting": ["problem", "solution", "symptom", "cause"],
            "specifications": ["specification", "parameter", "value", "range"]
        }
        
        for table_type, keywords in type_patterns.items():
            if any(keyword in header_text for keyword in keywords):
                return table_type
        
        return "general"

# =============================================================================
# 3. METADATA ENRICHMENT - Add semantic tags for better filtering
# =============================================================================

class MetadataEnricher:
    def __init__(self):
        # Define patterns for different content types
        self.content_patterns = {
            "component_details": [
                r"component|part|assembly|unit",
                r"specification|specs|parameters",
                r"model|type|series"
            ],
            "maintenance_schedule": [
                r"maintenance|service|inspection",
                r"schedule|interval|frequency",
                r"daily|weekly|monthly|quarterly|annually"
            ],
            "spare_parts": [
                r"spare|replacement|parts",
                r"inventory|stock|catalog",
                r"part\s*number|p/n|pn"
            ],
            "operation_manual": [
                r"operation|operating|procedure",
                r"instruction|step|process",
                r"start|stop|control"
            ],
            "safety_guidelines": [
                r"safety|warning|caution|danger",
                r"hazard|risk|protection",
                r"ppe|protective|equipment"
            ]
        }
        
        self.criticality_keywords = {
            "high": ["critical", "essential", "vital", "mandatory", "required"],
            "medium": ["important", "recommended", "should", "advised"],
            "low": ["optional", "suggested", "may", "could"]
        }
    
    def enrich_text_metadata(self, text: str, section: str, page_num: int) -> Dict:
        """Add metadata tags to text content"""
        metadata = {
            "page_number": page_num,
            "section": section,
            "content_type": self._classify_content_type(text),
            "criticality": self._assess_criticality(text),
            "entities": self._extract_entities(text),
            "keywords": self._extract_keywords(text),
            "word_count": len(text.split())
        }
        
        return metadata
    
    def enrich_table_metadata(self, table_data: Dict) -> Dict:
        """Add metadata tags to table content"""
        headers = table_data.get("headers", [])
        natural_lang = table_data.get("natural_language", "")
        
        metadata = {
            "page_number": table_data.get("page_number"),
            "table_type": table_data.get("table_type"),
            "content_type": "tabular_data",
            "columns": headers,
            "row_count": table_data.get("row_count", 0),
            "entities": self._extract_entities(natural_lang),
            "contains_part_numbers": self._contains_part_numbers(natural_lang),
            "contains_measurements": self._contains_measurements(natural_lang)
        }
        
        return metadata
    
    def _classify_content_type(self, text: str) -> str:
        """Classify content type based on text patterns"""
        text_lower = text.lower()
        
        for content_type, patterns in self.content_patterns.items():
            if any(re.search(pattern, text_lower) for pattern in patterns):
                return content_type
        
        return "general"
    
    def _assess_criticality(self, text: str) -> str:
        """Assess criticality level of content"""
        text_lower = text.lower()
        
        for level, keywords in self.criticality_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return level
        
        return "medium"  # Default
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract relevant entities from text"""
        entities = {
            "part_numbers": re.findall(r'[A-Z]{2,}-\d{3,}[A-Z]?', text),
            "measurements": re.findall(r'\d+\.?\d*\s*(?:mm|cm|m|kg|psi|gpm|°C|°F)', text),
            "model_numbers": re.findall(r'[A-Z]+\d+[A-Z]*', text),
            "serial_numbers": re.findall(r'S/N:?\s*([A-Z0-9]+)', text)
        }
        
        return {k: v for k, v in entities.items() if v}  # Remove empty lists
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from text"""
        # Simple keyword extraction - in production, use NLP libraries
        words = re.findall(r'\b[a-zA-Z]{4,}\b', text.lower())
        
        # Filter out common words
        stop_words = {'with', 'from', 'they', 'been', 'have', 'were', 'this', 'that'}
        keywords = [w for w in words if w not in stop_words]
        
        # Return most frequent keywords
        from collections import Counter
        return [word for word, count in Counter(keywords).most_common(10)]
    
    def _contains_part_numbers(self, text: str) -> bool:
        """Check if text contains part numbers"""
        return bool(re.search(r'[A-Z]{2,}-\d{3,}', text))
    
    def _contains_measurements(self, text: str) -> bool:
        """Check if text contains measurements"""
        return bool(re.search(r'\d+\.?\d*\s*(?:mm|cm|m|kg|psi|gpm|°C|°F)', text))

# =============================================================================
# 4. CHUNK STRATEGY - Create focused chunks maintaining relationships
# =============================================================================

@dataclass
class DocumentChunk:
    chunk_id: str
    content_type: str
    text_content: str
    structured_data: Dict
    metadata: Dict
    relationships: List[str]  # IDs of related chunks

class ChunkingStrategy:
    def __init__(self):
        self.text_extractor = None
        self.table_converter = None
        self.metadata_enricher = MetadataEnricher()
        self.chunks = []
    
    def create_chunks(self, pdf_path: str) -> List[DocumentChunk]:
        """Create focused chunks from PDF while maintaining relationships"""
        self.text_extractor = TextExtractor(pdf_path)
        self.table_converter = TableConverter(pdf_path)
        
        # Extract all content
        pages_data = self.text_extractor.extract_text_with_context()
        tables_data = self.table_converter.extract_tables_with_context()
        
        chunks = []
        
        # Process text content
        for page_data in pages_data:
            page_chunks = self._create_text_chunks(page_data)
            chunks.extend(page_chunks)
        
        # Process table content
        for table_data in tables_data:
            table_chunk = self._create_table_chunk(table_data)
            chunks.append(table_chunk)
        
        # Create relationships between chunks
        self._establish_relationships(chunks)
        
        return chunks
    
    def _create_text_chunks(self, page_data: Dict) -> List[DocumentChunk]:
        """Create chunks from text content"""
        chunks = []
        current_section = ""
        
        for paragraph in page_data["paragraphs"]:
            # Update current section based on nearby headers
            section_context = self._get_section_context(paragraph, page_data["sections"])
            
            # Create chunk for substantial paragraphs
            if len(paragraph["text"]) > 100:  # Minimum chunk size
                chunk_id = f"text_{page_data['page_number']}_{len(chunks)}"
                
                # Enrich with metadata
                metadata = self.metadata_enricher.enrich_text_metadata(
                    paragraph["text"], 
                    section_context, 
                    page_data["page_number"]
                )
                
                chunk = DocumentChunk(
                    chunk_id=chunk_id,
                    content_type="text",
                    text_content=paragraph["text"],
                    structured_data={"section": section_context},
                    metadata=metadata,
                    relationships=[]
                )
                
                chunks.append(chunk)
        
        return chunks
    
    def _create_table_chunk(self, table_data: Dict) -> DocumentChunk:
        """Create chunk from table data"""
        chunk_id = f"table_{table_data['page_number']}_{table_data['table_index']}"
        
        # Enrich with metadata
        metadata = self.metadata_enricher.enrich_table_metadata(table_data)
        
        chunk = DocumentChunk(
            chunk_id=chunk_id,
            content_type="table",
            text_content=table_data["natural_language"],
            structured_data=table_data["structured_data"],
            metadata=metadata,
            relationships=[]
        )
        
        return chunk
    
    def _get_section_context(self, paragraph: Dict, sections: List[Dict]) -> str:
        """Get the section context for a paragraph"""
        # Find the nearest section header before this paragraph
        paragraph_y = paragraph["bbox"][1]  # Y coordinate
        
        relevant_section = "unknown"
        for section in sections:
            section_y = section["bbox"][1]
            if section_y < paragraph_y:  # Section is above paragraph
                relevant_section = section["text"]
        
        return relevant_section
    
    def _establish_relationships(self, chunks: List[DocumentChunk]):
        """Establish relationships between related chunks"""
        for i, chunk in enumerate(chunks):
            relationships = []
            
            # Find chunks with shared entities
            chunk_entities = chunk.metadata.get("entities", {})
            
            for j, other_chunk in enumerate(chunks):
                if i != j:
                    other_entities = other_chunk.metadata.get("entities", {})
                    
                    # Check for shared part numbers
                    shared_parts = set(chunk_entities.get("part_numbers", [])) & \
                                 set(other_entities.get("part_numbers", []))
                    
                    if shared_parts:
                        relationships.append(other_chunk.chunk_id)
                    
                    # Check for same page proximity
                    if (chunk.metadata.get("page_number") == 
                        other_chunk.metadata.get("page_number")):
                        relationships.append(other_chunk.chunk_id)
            
            chunk.relationships = relationships[:5]  # Limit to top 5 relationships

# =============================================================================
# MAIN PROCESSING PIPELINE
# =============================================================================

def process_pdf_for_rag(pdf_path: str) -> List[Dict]:
    """Main function to process PDF into RAG-ready chunks"""
    chunking_strategy = ChunkingStrategy()
    chunks = chunking_strategy.create_chunks(pdf_path)

    # Convert to dictionary format for storage
    processed_chunks = []
    for chunk in chunks:
        chunk_dict = {
            "chunk_id": chunk.chunk_id,
            "content_type": chunk.content_type,
            "text_content": chunk.text_content,
            "structured_data": chunk.structured_data,
            "metadata": chunk.metadata,
            "relationships": chunk.relationships
        }
        processed_chunks.append(chunk_dict)

    return processed_chunks

def save_chunks_to_files(chunks: List[Dict], output_folder: str = "chunks_output"):
    """Save each chunk to a separate file in the specified folder"""
    # Create output folder if it doesn't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # Create timestamp for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save summary file
    summary_file = os.path.join(output_folder, f"chunks_summary_{timestamp}.txt")
    with open(summary_file, "w", encoding="utf-8") as f:
        f.write(f"PDF Chunks Summary\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total chunks: {len(chunks)}\n")
        f.write("="*60 + "\n\n")

        for i, chunk in enumerate(chunks, 1):
            f.write(f"Chunk {i}: {chunk['chunk_id']}\n")
            f.write(f"  Type: {chunk['content_type']}\n")
            f.write(f"  Page: {chunk['metadata'].get('page_number', 'N/A')}\n")
            f.write(f"  File: chunk_{i:03d}_{chunk['chunk_id']}.json\n")
            f.write(f"  Content length: {len(chunk['text_content'])} characters\n\n")

    # Save each chunk to individual files as pure JSON
    for i, chunk in enumerate(chunks, 1):
        # Create filename
        filename = f"chunk_{i:03d}_{chunk['chunk_id']}.json"
        filepath = os.path.join(output_folder, filename)

        # Write chunk as pure JSON
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(chunk, f, indent=2, ensure_ascii=False)

    return output_folder, len(chunks)

# Example usage
if __name__ == "__main__":
    # Process a PDF file
    pdf_path = "tcm.pdf"
    chunks = process_pdf_for_rag(pdf_path)

    # Save processed chunks to JSON
    with open("processed_chunks.json", "w") as f:
        json.dump(chunks, f, indent=2)

    # Save each chunk to individual files
    output_folder, chunk_count = save_chunks_to_files(chunks, "chunks_output")

    print(f"Processed {len(chunks)} chunks from PDF")
    print(f"Saved {chunk_count} individual chunk files to '{output_folder}' folder")
    print("="*80)

    # Print each chunk with detailed formatting
    for i, chunk in enumerate(chunks, 1):
        print(f"\n{'='*60}")
        print(f"CHUNK {i} of {len(chunks)}")
        print(f"{'='*60}")

        print(f"Chunk ID: {chunk['chunk_id']}")
        print(f"Content Type: {chunk['content_type']}")
        print(f"Page Number: {chunk['metadata'].get('page_number', 'N/A')}")

        if chunk['content_type'] == 'table':
            print(f"Table Type: {chunk['metadata'].get('table_type', 'N/A')}")
            print(f"Headers: {chunk['metadata'].get('headers', [])}")

        print(f"\nText Content:")
        print("-" * 40)
        print(chunk['text_content'])

        if chunk['structured_data']:
            print(f"\nStructured Data:")
            print("-" * 40)
            print(json.dumps(chunk['structured_data'], indent=2))

        if chunk['relationships']:
            print(f"\nRelationships:")
            print("-" * 40)
            if isinstance(chunk['relationships'], dict):
                for rel_type, rel_data in chunk['relationships'].items():
                    print(f"  {rel_type}: {rel_data}")
            else:
                for rel in chunk['relationships']:
                    print(f"  {rel}")

        print(f"\nMetadata:")
        print("-" * 40)
        for key, value in chunk['metadata'].items():
            if key not in ['headers', 'table_type', 'page_number']:  # Already printed above
                print(f"  {key}: {value}")

        # Add separator between chunks
        if i < len(chunks):
            print("\n" + "."*80)