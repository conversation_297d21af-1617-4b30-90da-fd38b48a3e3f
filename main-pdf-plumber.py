import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
import pandas as pd
import json
import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from transformers import pipeline
import tiktoken
import base64
import io


# Usage example and utility functions
def main():

    pdf = pdfplumber.open("fwg-1.pdf")

    # Get total number of pages
    total_pages = len(pdf.pages)
    print(f"Total pages in document: {total_pages}")

    # Initialize counters for all pages
    total_tables = 0
    total_images = 0
    total_table_images = 0

    # Process each page
    for page_num, page in enumerate(pdf.pages, 1):
        print(f"\n{'='*60}")
        print(f"PROCESSING PAGE {page_num}")
        print(f"{'='*60}")

        # Find tables on the page
        tables = page.find_tables(table_settings={})
        extracted_tables = page.extract_tables(table_settings={})
        print("extracted_tables")
        print(extracted_tables)
        # Find images on the page
        images = page.images

        # Count table images for this page
        def is_table_image_simple(image, tables):
            """Simplified version for counting across all pages"""
            img_x0 = image.get('x0', 0)
            img_y0 = image.get('y0', 0)
            img_x1 = image.get('x1', 0)
            img_y1 = image.get('y1', 0)
            img_width = image.get('width', 0)
            img_height = image.get('height', 0)

            # Check overlap with tables
            for table in tables:
                table_bbox = table.bbox
                if table_bbox:
                    t_x0, t_y0, t_x1, t_y1 = table_bbox
                    overlap_x = max(0, min(img_x1, t_x1) - max(img_x0, t_x0))
                    overlap_y = max(0, min(img_y1, t_y1) - max(img_y0, t_y0))

                    if overlap_x > 0 and overlap_y > 0:
                        overlap_area = overlap_x * overlap_y
                        img_area = img_width * img_height
                        if img_area > 0 and (overlap_area / img_area) > 0.3:
                            return True

            # Check characteristics
            aspect_ratio = img_width / img_height if img_height > 0 else 0
            is_1bit = image.get('bits') == 1
            is_mask = image.get('imagemask') is True

            if is_1bit and is_mask and aspect_ratio > 1.5:
                return True

            return False

        page_table_images = sum(1 for img in images if is_table_image_simple(img, tables))

        # Update totals
        total_tables += len(extracted_tables)
        total_images += len(images)
        total_table_images += page_table_images

        # Print counts for this page
        print(f"Tables found on page {page_num}: {len(tables)}")
        print(f"Extracted tables on page {page_num}: {len(extracted_tables)}")
        print(f"Images found on page {page_num}: {len(images)}")
        print(f"Table-related images on page {page_num}: {page_table_images}")

        # Only show detailed output for first page to avoid too much output
        if page_num == 1:
            # Print each table individually
            print("\n" + "="*50)
            print("EXTRACTED TABLES (PAGE 1):")
            print("="*50)

            for i, table in enumerate(extracted_tables, 1):
                print(f"\nTable {i}:")
                print("-" * 30)
                if table:
                    for row_idx, row in enumerate(table):
                        print(f"Row {row_idx + 1}: {row}")
                else:
                    print("Empty table")

            print("\n" + "="*50)
            print("FOUND TABLES METADATA (PAGE 1):")
            print("="*50)

            for i, table in enumerate(tables, 1):
                print(f"\nTable {i} metadata:")
                print("-" * 30)
                print(f"Bounding box: {table.bbox}")
                print(f"Rows: {len(table.rows) if hasattr(table, 'rows') else 'N/A'}")
                print(f"Cells: {len(table.cells) if hasattr(table, 'cells') else 'N/A'}")

            # Function to check if an image is likely a table
            def is_table_image(image, tables):
                """
                Determine if an image is likely representing a table based on:
                1. Position overlap with detected tables
                2. Aspect ratio (tables are often wider than tall)
                3. Image characteristics (1-bit images are often line drawings/tables)
                """
                img_x0 = image.get('x0', 0)
                img_y0 = image.get('y0', 0)
                img_x1 = image.get('x1', 0)
                img_y1 = image.get('y1', 0)
                img_width = image.get('width', 0)
                img_height = image.get('height', 0)

                # Check if image overlaps with any detected table
                for table in tables:
                    table_bbox = table.bbox
                    if table_bbox:
                        t_x0, t_y0, t_x1, t_y1 = table_bbox

                        # Check for overlap
                        overlap_x = max(0, min(img_x1, t_x1) - max(img_x0, t_x0))
                        overlap_y = max(0, min(img_y1, t_y1) - max(img_y0, t_y0))

                        if overlap_x > 0 and overlap_y > 0:
                            overlap_area = overlap_x * overlap_y
                            img_area = img_width * img_height

                            # If significant overlap (>30% of image area), likely a table
                            if img_area > 0 and (overlap_area / img_area) > 0.3:
                                return True, f"Overlaps with table at {table_bbox}"

                # Check image characteristics that suggest it's a table
                aspect_ratio = img_width / img_height if img_height > 0 else 0
                is_1bit = image.get('bits') == 1
                is_mask = image.get('imagemask') is True

                # Tables are often:
                # - 1-bit images (line drawings)
                # - Image masks (black and white line art)
                # - Have wide aspect ratios (wider than tall)
                if is_1bit and is_mask and aspect_ratio > 1.5:
                    return True, f"1-bit mask with table-like aspect ratio ({aspect_ratio:.2f})"

                # Check if positioned near tables (within 50 points)
                for table in tables:
                    table_bbox = table.bbox
                    if table_bbox:
                        t_x0, t_y0, t_x1, t_y1 = table_bbox

                        # Check proximity
                        distance = min(
                            abs(img_x0 - t_x1),  # Distance from left edge to table right
                            abs(img_x1 - t_x0),  # Distance from right edge to table left
                            abs(img_y0 - t_y1),  # Distance from top edge to table bottom
                            abs(img_y1 - t_y0)   # Distance from bottom edge to table top
                        )

                        if distance < 50:  # Within 50 points
                            return True, f"Near table at {table_bbox} (distance: {distance:.1f})"

                return False, "Not identified as table-related"

            # Print only table-related images
            print("\n" + "="*50)
            print("TABLE-RELATED IMAGES (PAGE 1):")
            print("="*50)

            table_image_count = 0
            for i, image in enumerate(images, 1):
                is_table, reason = is_table_image(image, tables)

                if is_table:
                    table_image_count += 1
                    print(f"\nTable Image {table_image_count} (Original Image {i}):")
                    print("-" * 40)
                    print(f"Reason: {reason}")
                    print(f"Bounding box: ({image.get('x0', 'N/A')}, {image.get('y0', 'N/A')}, {image.get('x1', 'N/A')}, {image.get('y1', 'N/A')})")
                    print(f"Width: {image.get('width', 'N/A')}")
                    print(f"Height: {image.get('height', 'N/A')}")
                    print(f"Source size: {image.get('srcsize', 'N/A')}")
                    print(f"Name: {image.get('name', 'N/A')}")
                    print(f"Colorspace: {image.get('colorspace', 'N/A')}")
                    print(f"Bits per component: {image.get('bits', 'N/A')}")
                    print(f"Image mask: {image.get('imagemask', 'N/A')}")

                    # Extract and encode image data as base64
                    try:
                        stream = image.get('stream')
                        if stream:
                            # Get the raw image data
                            image_data = stream.get_data()

                            # Encode to base64
                            base64_data = base64.b64encode(image_data).decode('utf-8')

                            print(f"Data size: {len(image_data)} bytes")
                            print(f"Base64 data (first 100 chars): {base64_data[:100]}...")
                            print(f"Base64 data (full):")
                            print(base64_data)
                            print()
                        else:
                            print("No stream data available")
                    except Exception as e:
                        print(f"Error extracting image data: {e}")

            print(f"\nSummary: Found {table_image_count} table-related images out of {len(images)} total images on page 1")

    # Print summary for all pages
    print(f"\n{'='*60}")
    print("DOCUMENT SUMMARY:")
    print(f"{'='*60}")
    print(f"Total pages: {total_pages}")
    print(f"Total tables found: {total_tables}")
    print(f"Total images found: {total_images}")
    print(f"Total table-related images: {total_table_images}")
    print(f"Percentage of images that are table-related: {(total_table_images/total_images*100):.1f}%" if total_images > 0 else "N/A")

    pdf.close()

if __name__ == "__main__":
    main()