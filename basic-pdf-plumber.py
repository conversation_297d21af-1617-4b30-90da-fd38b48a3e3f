import pdfplumber
import pymupdf4llm

def main():
    with pdfplumber.open("tcm.pdf") as pdf:
        for page_num, page in enumerate(pdf.pages):
            text = page.extract_text()
            print(f"[PAGE {page_num+1}]\n{text}\n")

def extract_with_pymupdf():
    # Extracts with markdown-like structure
    md_text = pymupdf4llm.to_markdown('tcm.pdf')
    print(md_text)

if __name__ == "__main__":
    extract_with_pymupdf()            