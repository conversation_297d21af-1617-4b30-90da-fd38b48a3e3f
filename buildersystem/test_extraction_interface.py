#!/usr/bin/env python3
"""
Test script for the updated extraction interface with folder-based system prompts and schemas.
"""

import os
from structured_query import load_available_extractions, StructuredDataExtractor

def test_extraction_loading():
    """Test loading of extraction types from folders."""
    print("🧪 Testing extraction type loading...")
    
    # Load available extractions
    extractions = load_available_extractions()
    
    print(f"Found {len(extractions)} extraction types:")
    for key, config in extractions.items():
        print(f"  - {key}: {config['name']}")
        print(f"    System prompt length: {len(config['system_prompt'])} characters")
        print(f"    Schema type: {config['schema'].get('type', 'unknown')}")
        print()
    
    return extractions

def test_spare_parts_extraction():
    """Test spare parts extraction with a small sample."""
    print("🔧 Testing spare parts extraction...")
    
    # Load extractions
    extractions = load_available_extractions()
    
    if 'spare_parts' not in extractions:
        print("❌ Spare parts extraction not found!")
        return
    
    # Initialize extractor
    extractor = StructuredDataExtractor()
    
    # Get a few pages for testing
    pages = extractor.get_file_pages_ordered('TANK CLEANING MACHINE.pdf')[:2]
    
    if not pages:
        print("❌ No pages found for testing!")
        return
    
    print(f"Testing with {len(pages)} pages...")
    
    # Get extraction config
    config = extractions['spare_parts']
    schema = config['schema']
    system_prompt = config['system_prompt']
    
    # Run extraction
    result = extractor.extract_structured_data(pages, schema, system_prompt)
    
    if result.get('success'):
        data = result['data']
        print(f"✅ Extraction successful! Found {len(data)} items")
        if data:
            print("Sample item:")
            print(f"  Equipment: {data[0].get('equipment_name', 'N/A')}")
            print(f"  Part: {data[0].get('part_name', 'N/A')}")
            print(f"  Part Number: {data[0].get('part_number', 'N/A')}")
    else:
        print(f"❌ Extraction failed: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    print("🚀 Testing Updated Extraction Interface\n")
    
    # Set environment
    os.environ['LLM_MODEL'] = 'gpt-4o-mini'
    
    # Test loading
    extractions = test_extraction_loading()
    
    # Test extraction if available
    if extractions:
        test_spare_parts_extraction()
    else:
        print("❌ No extractions available for testing")
    
    print("\n✅ Testing complete!")
