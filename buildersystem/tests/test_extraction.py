#!/usr/bin/env python3
"""
Tests for extraction functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import List

from src.extraction.extractor import LLMExtractor
from src.extraction.models import SparePart, SparePartsList
from src.core.base_classes import ChunkData, ExtractionType, ExtractionResult, TokenUsage


class TestLLMExtractor:
    """Test LLM extractor functionality."""
    
    @pytest.fixture
    def mock_chunks(self):
        """Create mock chunks for testing."""
        return [
            ChunkData(
                text="Spare part: Engine Oil Filter, Part No: OF-123, Quantity: 2",
                text_raw="Spare part: Engine Oil Filter, Part No: OF-123, Quantity: 2",
                page_number=1,
                file_name="test.pdf",
                metadata={}
            ),
            ChunkData(
                text="Component: Main Engine, Model: ABC-500, Serial: 12345",
                text_raw="Component: Main Engine, Model: ABC-500, Serial: 12345",
                page_number=2,
                file_name="test.pdf",
                metadata={}
            )
        ]
    
    @pytest.fixture
    def mock_system_prompt(self):
        """Create mock system prompt."""
        return "Extract spare parts data from the provided text."
    
    @pytest.fixture
    def mock_schema(self):
        """Create mock schema."""
        return {
            "type": "object",
            "properties": {
                "parts": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "part_number": {"type": "string"},
                            "quantity": {"type": "integer"}
                        }
                    }
                }
            }
        }
    
    @patch('src.extraction.extractor.ChatOpenAI')
    def test_extractor_initialization_openai(self, mock_openai):
        """Test extractor initialization with OpenAI."""
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            extractor = LLMExtractor(model_name="gpt-4")
            
            assert extractor.model_name == "gpt-4"
            assert extractor.provider == "openai"
            mock_openai.assert_called_once()
    
    @patch('src.extraction.extractor.ChatAnthropic')
    def test_extractor_initialization_anthropic(self, mock_anthropic):
        """Test extractor initialization with Anthropic."""
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.anthropic_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "anthropic"
            
            extractor = LLMExtractor(model_name="claude-3")
            
            assert extractor.model_name == "claude-3"
            assert extractor.provider == "anthropic"
            mock_anthropic.assert_called_once()
    
    def test_prepare_context(self, mock_chunks):
        """Test context preparation from chunks."""
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            with patch('src.extraction.extractor.ChatOpenAI'):
                extractor = LLMExtractor()
                context = extractor._prepare_context(mock_chunks)
                
                assert isinstance(context, str)
                assert "[Page 1]" in context
                assert "[Page 2]" in context
                assert "Engine Oil Filter" in context
                assert "Main Engine" in context
    
    def test_estimate_tokens(self):
        """Test token estimation."""
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            with patch('src.extraction.extractor.ChatOpenAI'):
                extractor = LLMExtractor()
                
                # Test input token estimation
                tokens = extractor._estimate_input_tokens("test context", "test prompt")
                assert isinstance(tokens, int)
                assert tokens > 0
                
                # Test output token estimation
                mock_result = Mock()
                mock_result.model_dump.return_value = {"test": "data"}
                tokens = extractor._estimate_output_tokens(mock_result)
                assert isinstance(tokens, int)
                assert tokens > 0
    
    @patch('src.extraction.extractor.get_model_for_extraction_type')
    def test_extract_success(self, mock_get_model, mock_chunks, mock_system_prompt, mock_schema):
        """Test successful extraction."""
        # Mock the model class
        mock_model_class = Mock()
        mock_get_model.return_value = mock_model_class
        
        # Mock the LLM and structured output
        mock_llm = Mock()
        mock_structured_llm = Mock()
        mock_result = SparePartsList(spare_parts=[
            SparePart(
                equipment_name="Engine",
                part_name="Oil Filter",
                part_number="OF-123",
                quantity=2
            )
        ])
        
        mock_structured_llm.invoke.return_value = mock_result
        mock_llm.with_structured_output.return_value = mock_structured_llm
        
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            with patch('src.extraction.extractor.ChatOpenAI', return_value=mock_llm):
                extractor = LLMExtractor()
                
                result = extractor.extract(
                    mock_chunks,
                    ExtractionType.SPARE_PARTS,
                    mock_system_prompt,
                    mock_schema
                )
                
                assert isinstance(result, ExtractionResult)
                assert result.success is True
                assert result.data is not None
                assert len(result.data) > 0
                assert result.token_usage is not None
    
    def test_extract_empty_chunks(self, mock_system_prompt, mock_schema):
        """Test extraction with empty chunks."""
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            with patch('src.extraction.extractor.ChatOpenAI'):
                extractor = LLMExtractor()
                
                result = extractor.extract(
                    [],
                    ExtractionType.SPARE_PARTS,
                    mock_system_prompt,
                    mock_schema
                )
                
                assert isinstance(result, ExtractionResult)
                assert result.success is False
                assert "No chunks provided" in result.error
    
    @patch('src.extraction.extractor.get_model_for_extraction_type')
    def test_extract_with_fallback(self, mock_get_model, mock_chunks, mock_system_prompt, mock_schema):
        """Test extraction with fallback strategy."""
        mock_model_class = Mock()
        mock_get_model.return_value = mock_model_class
        
        # Mock LLM to fail first, then succeed
        mock_llm = Mock()
        mock_structured_llm = Mock()
        
        # First call fails, second succeeds
        mock_structured_llm.invoke.side_effect = [
            Exception("First attempt failed"),
            SparePartsList(spare_parts=[SparePart(equipment_name="Test", part_name="Test Part")])
        ]
        mock_llm.with_structured_output.return_value = mock_structured_llm
        
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            with patch('src.extraction.extractor.ChatOpenAI', return_value=mock_llm):
                extractor = LLMExtractor()
                
                result = extractor.extract_with_fallback(
                    mock_chunks,
                    ExtractionType.SPARE_PARTS,
                    mock_system_prompt,
                    mock_schema,
                    max_retries=1
                )
                
                assert isinstance(result, ExtractionResult)
                # Should succeed on retry
                assert result.success is True
    
    def test_validate_and_extract(self, mock_chunks, mock_system_prompt, mock_schema):
        """Test validation and extraction."""
        with patch('src.config.settings.settings') as mock_settings:
            mock_settings.models.openai_api_key = "test_key"
            mock_settings.get_model_provider.return_value = "openai"
            
            with patch('src.extraction.extractor.ChatOpenAI'):
                extractor = LLMExtractor()
                
                # Mock the extract_with_fallback method
                mock_result = ExtractionResult(
                    success=True,
                    data=[{"equipment_name": "Test", "part_name": "Test Part"}],
                    extraction_method="test"
                )
                
                with patch.object(extractor, 'extract_with_fallback', return_value=mock_result):
                    result = extractor.validate_and_extract(
                        mock_chunks,
                        ExtractionType.SPARE_PARTS,
                        mock_system_prompt,
                        mock_schema
                    )
                    
                    assert isinstance(result, ExtractionResult)
                    assert result.success is True


class TestExtractionModels:
    """Test extraction model functionality."""
    
    def test_spare_part_model(self):
        """Test SparePart model creation and validation."""
        spare_part = SparePart(
            equipment_name="Main Engine",
            part_name="Oil Filter",
            part_number="OF-123",
            quantity=2
        )
        
        assert spare_part.equipment_name == "Main Engine"
        assert spare_part.part_name == "Oil Filter"
        assert spare_part.part_number == "OF-123"
        assert spare_part.quantity == 2
    
    def test_spare_parts_list_model(self):
        """Test SparePartsList model."""
        parts_list = SparePartsList(spare_parts=[
            SparePart(equipment_name="Engine", part_name="Filter"),
            SparePart(equipment_name="Pump", part_name="Seal")
        ])
        
        assert len(parts_list.spare_parts) == 2
        assert parts_list.spare_parts[0].equipment_name == "Engine"
        assert parts_list.spare_parts[1].equipment_name == "Pump"
    
    def test_model_validation(self):
        """Test model validation."""
        # Test with invalid data - Pydantic v2 raises ValidationError, not ValueError
        from pydantic import ValidationError
        with pytest.raises(ValidationError):
            SparePart(
                equipment_name="",  # Empty string should be invalid
                part_name="Filter"
            )


if __name__ == "__main__":
    pytest.main([__file__])
