#!/usr/bin/env python3
"""
Tests for configuration management.
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.config.settings import Settings, DatabaseConfig, ModelConfig
from src.config.config_manager import Config<PERSON>anager, ExtractionConfiguration
from src.core.base_classes import ExtractionType, ConfigurationError


class TestSettings:
    """Test settings configuration."""
    
    def test_settings_initialization(self):
        """Test settings initialization."""
        settings = Settings()
        
        assert settings.project_root.exists()
        assert settings.data_dir.exists()
        assert settings.output_dir.exists()
        assert settings.logs_dir.exists()
        assert settings.temp_dir.exists()
    
    @patch.dict('os.environ', {
        'SUPABASE_URL': 'test_url',
        'SUPABASE_KEY': 'test_key',
        'OPENAI_API_KEY': 'test_openai_key'
    })
    def test_settings_from_environment(self):
        """Test loading settings from environment variables."""
        settings = Settings()
        
        assert settings.database.supabase_url == 'test_url'
        assert settings.database.supabase_key == 'test_key'
        assert settings.models.openai_api_key == 'test_openai_key'
    
    def test_get_available_models(self):
        """Test getting available models."""
        settings = Settings()
        models = settings.get_available_models()
        
        # Should return empty dict if no API keys configured
        assert isinstance(models, dict)
    
    def test_validate_configuration(self):
        """Test configuration validation."""
        settings = Settings()
        issues = settings.validate_configuration()
        
        # Should have issues if not properly configured
        assert isinstance(issues, list)
    
    def test_get_model_provider(self):
        """Test model provider detection."""
        settings = Settings()
        
        assert settings.get_model_provider("gpt-4") == "openai"
        assert settings.get_model_provider("claude-3") == "anthropic"
        assert settings.get_model_provider("mistral-large") == "mistral"
        assert settings.get_model_provider("unknown-model") == "openai"  # Default


class TestConfigManager:
    """Test configuration manager."""
    
    @pytest.fixture
    def temp_config_dir(self):
        """Create temporary configuration directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create directory structure
            schemas_dir = temp_path / "schemas"
            prompts_dir = temp_path / "prompts"
            queries_dir = temp_path / "queries"
            
            schemas_dir.mkdir()
            prompts_dir.mkdir()
            queries_dir.mkdir()
            
            # Create test files
            test_schema = {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "value": {"type": "number"}
                }
            }
            
            (schemas_dir / "spare_parts.json").write_text(json.dumps(test_schema))
            (prompts_dir / "spare_parts.txt").write_text("Test prompt for spare parts extraction")
            (queries_dir / "spare_parts.txt").write_text("spare parts\ncomponents\nequipment")
            
            yield temp_path
    
    def test_config_manager_initialization(self, temp_config_dir):
        """Test config manager initialization."""
        config_manager = ConfigManager(temp_config_dir)
        
        assert config_manager.data_dir == temp_config_dir
        assert config_manager.schemas_dir.exists()
        assert config_manager.prompts_dir.exists()
        assert config_manager.queries_dir.exists()
    
    def test_load_schema(self, temp_config_dir):
        """Test loading schema files."""
        config_manager = ConfigManager(temp_config_dir)
        
        schema = config_manager.load_schema(ExtractionType.SPARE_PARTS)
        
        assert isinstance(schema, dict)
        assert "type" in schema
        assert schema["type"] == "object"
    
    def test_load_prompt(self, temp_config_dir):
        """Test loading prompt files."""
        config_manager = ConfigManager(temp_config_dir)
        
        prompt = config_manager.load_prompt(ExtractionType.SPARE_PARTS)
        
        assert isinstance(prompt, str)
        assert "spare parts" in prompt.lower()
    
    def test_load_queries(self, temp_config_dir):
        """Test loading query files."""
        config_manager = ConfigManager(temp_config_dir)
        
        queries = config_manager.load_queries(ExtractionType.SPARE_PARTS)
        
        assert isinstance(queries, list)
        assert len(queries) == 3
        assert "spare parts" in queries
    
    def test_get_extraction_config(self, temp_config_dir):
        """Test getting complete extraction configuration."""
        config_manager = ConfigManager(temp_config_dir)
        
        config = config_manager.get_extraction_config(ExtractionType.SPARE_PARTS)
        
        assert isinstance(config, ExtractionConfiguration)
        assert config.extraction_type == ExtractionType.SPARE_PARTS
        assert config.schema is not None
        assert config.system_prompt is not None
        assert config.search_queries is not None
    
    def test_validate_extraction_config(self, temp_config_dir):
        """Test extraction configuration validation."""
        config_manager = ConfigManager(temp_config_dir)

        issues = config_manager.validate_extraction_config(ExtractionType.SPARE_PARTS)

        assert isinstance(issues, list)
        # May have issues with minimal test config, just check it returns a list
        assert len(issues) >= 0
    
    def test_missing_schema_file(self, temp_config_dir):
        """Test handling of missing schema file."""
        config_manager = ConfigManager(temp_config_dir)
        
        with pytest.raises(ConfigurationError):
            config_manager.load_schema(ExtractionType.COMPONENT_EXTRACTION)
    
    def test_invalid_json_schema(self, temp_config_dir):
        """Test handling of invalid JSON schema."""
        config_manager = ConfigManager(temp_config_dir)

        # Create invalid JSON file for spare_parts
        invalid_json_file = temp_config_dir / "schemas" / "spare_parts.json"
        invalid_json_file.write_text("{ invalid json }")

        with pytest.raises(ConfigurationError):
            config_manager.load_schema(ExtractionType.SPARE_PARTS)
    
    def test_reload_config(self, temp_config_dir):
        """Test configuration reloading."""
        config_manager = ConfigManager(temp_config_dir)
        
        # Load config first
        config1 = config_manager.get_extraction_config(ExtractionType.SPARE_PARTS)
        
        # Reload specific config
        config_manager.reload_config(ExtractionType.SPARE_PARTS)
        
        # Load again
        config2 = config_manager.get_extraction_config(ExtractionType.SPARE_PARTS)
        
        # Should be equivalent but not the same object
        assert config1.extraction_type == config2.extraction_type
        assert config1.schema == config2.schema
    
    def test_get_available_extractions(self, temp_config_dir):
        """Test getting all available extractions."""
        config_manager = ConfigManager(temp_config_dir)
        
        extractions = config_manager.get_available_extractions()
        
        assert isinstance(extractions, dict)
        assert "spare_parts" in extractions
        
        spare_parts_config = extractions["spare_parts"]
        assert "schema" in spare_parts_config
        assert "system_prompt" in spare_parts_config
        assert "search_queries" in spare_parts_config


if __name__ == "__main__":
    pytest.main([__file__])
