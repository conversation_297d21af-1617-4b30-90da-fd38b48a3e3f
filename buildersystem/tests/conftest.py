#!/usr/bin/env python3
"""
Pytest configuration and shared fixtures.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to Python path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


@pytest.fixture(scope="session")
def temp_project_dir():
    """Create a temporary project directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create directory structure
        (temp_path / "data").mkdir()
        (temp_path / "data" / "schemas").mkdir()
        (temp_path / "data" / "prompts").mkdir()
        (temp_path / "data" / "queries").mkdir()
        (temp_path / "output").mkdir()
        (temp_path / "logs").mkdir()
        (temp_path / "temp").mkdir()
        
        yield temp_path


@pytest.fixture
def mock_settings(temp_project_dir):
    """Mock settings with temporary directories."""
    with patch('src.config.settings.settings') as mock_settings:
        mock_settings.project_root = temp_project_dir
        mock_settings.data_dir = temp_project_dir / "data"
        mock_settings.output_dir = temp_project_dir / "output"
        mock_settings.logs_dir = temp_project_dir / "logs"
        mock_settings.temp_dir = temp_project_dir / "temp"
        
        # Mock database config
        mock_settings.database.supabase_url = "https://test.supabase.co"
        mock_settings.database.supabase_key = "test_key"
        mock_settings.database.table_name = "pdf_documents"
        
        # Mock model config
        mock_settings.models.openai_api_key = "test_openai_key"
        mock_settings.models.anthropic_api_key = "test_anthropic_key"
        mock_settings.models.mistral_api_key = "test_mistral_key"
        mock_settings.models.default_model = "gpt-4"
        
        # Mock methods
        mock_settings.get_available_models.return_value = {
            "openai": ["gpt-4", "gpt-3.5-turbo"],
            "anthropic": ["claude-3-sonnet"],
            "mistral": ["mistral-large-latest"]
        }
        mock_settings.get_model_provider.return_value = "openai"
        mock_settings.validate_configuration.return_value = []
        
        yield mock_settings


@pytest.fixture
def sample_extraction_config():
    """Sample extraction configuration for testing."""
    return {
        "schema": {
            "type": "object",
            "properties": {
                "parts": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "equipment_name": {"type": "string"},
                            "part_name": {"type": "string"},
                            "part_number": {"type": "string"},
                            "quantity": {"type": "integer"}
                        },
                        "required": ["equipment_name", "part_name"]
                    }
                }
            }
        },
        "system_prompt": "Extract spare parts information from the provided text.",
        "search_queries": ["spare parts", "components", "equipment"]
    }


@pytest.fixture
def sample_chunks():
    """Sample chunk data for testing."""
    from src.core.base_classes import ChunkData
    
    return [
        ChunkData(
            text="Main Engine spare parts: Oil Filter (OF-123), quantity: 2",
            text_raw="Main Engine spare parts: Oil Filter (OF-123), quantity: 2",
            page_number=1,
            file_name="test_document.pdf",
            metadata={"source": "test"},
            similarity_score=0.9
        ),
        ChunkData(
            text="Generator components: Air Filter (AF-456), quantity: 1",
            text_raw="Generator components: Air Filter (AF-456), quantity: 1",
            page_number=2,
            file_name="test_document.pdf",
            metadata={"source": "test"},
            similarity_score=0.8
        ),
        ChunkData(
            text="Pump assembly parts: Seal Ring (SR-789), quantity: 4",
            text_raw="Pump assembly parts: Seal Ring (SR-789), quantity: 4",
            page_number=3,
            file_name="test_document.pdf",
            metadata={"source": "test"},
            similarity_score=0.7
        )
    ]


@pytest.fixture
def sample_extraction_result():
    """Sample extraction result for testing."""
    from src.core.base_classes import ExtractionResult, TokenUsage
    
    return ExtractionResult(
        success=True,
        data=[
            {
                "equipment_name": "Main Engine",
                "part_name": "Oil Filter",
                "part_number": "OF-123",
                "quantity": 2
            },
            {
                "equipment_name": "Generator",
                "part_name": "Air Filter",
                "part_number": "AF-456",
                "quantity": 1
            }
        ],
        extraction_method="llm_extraction",
        total_chunks=3,
        token_usage=TokenUsage(
            input_tokens=1500,
            output_tokens=300,
            total_tokens=1800,
            estimated_cost_usd=0.036
        ),
        metadata={
            "processing_time": 12.5,
            "model_name": "gpt-4",
            "extraction_type": "spare_parts"
        }
    )


@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing."""
    mock_client = Mock()
    mock_table = Mock()
    mock_client.table.return_value = mock_table
    
    # Mock common responses
    mock_response = Mock()
    mock_response.data = []
    mock_table.select.return_value.execute.return_value = mock_response
    mock_table.select.return_value.eq.return_value.execute.return_value = mock_response
    mock_table.select.return_value.order.return_value.execute.return_value = mock_response
    
    return mock_client


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    mock_client = Mock()
    
    # Mock embedding response
    mock_embedding_response = Mock()
    mock_embedding_response.data = [Mock(embedding=[0.1, 0.2, 0.3] * 512)]  # 1536 dimensions
    mock_client.embeddings.create.return_value = mock_embedding_response
    
    return mock_client


@pytest.fixture
def mock_llm_response():
    """Mock LLM response for testing."""
    from src.extraction.models import SparePartsList, SparePart
    
    return SparePartsList(
        parts=[
            SparePart(
                equipment_name="Main Engine",
                part_name="Oil Filter",
                part_number="OF-123",
                quantity=2
            ),
            SparePart(
                equipment_name="Generator",
                part_name="Air Filter",
                part_number="AF-456",
                quantity=1
            )
        ]
    )


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables."""
    test_env = {
        "SUPABASE_URL": "https://test.supabase.co",
        "SUPABASE_KEY": "test_supabase_key",
        "OPENAI_API_KEY": "test_openai_key",
        "ANTHROPIC_API_KEY": "test_anthropic_key",
        "MISTRAL_API_KEY": "test_mistral_key",
        "LANGCHAIN_API_KEY": "test_langchain_key",
        "LANGCHAIN_PROJECT": "test_project"
    }
    
    # Set environment variables
    for key, value in test_env.items():
        os.environ[key] = value
    
    yield
    
    # Clean up environment variables
    for key in test_env.keys():
        if key in os.environ:
            del os.environ[key]


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    # Add custom markers
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "requires_api: mark test as requiring API access"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers."""
    for item in items:
        # Mark tests that require API access
        if "test_api" in item.name or "test_llm" in item.name:
            item.add_marker(pytest.mark.requires_api)
        
        # Mark integration tests
        if "integration" in item.name or "test_end_to_end" in item.name:
            item.add_marker(pytest.mark.integration)
        
        # Mark slow tests
        if "test_large" in item.name or "test_performance" in item.name:
            item.add_marker(pytest.mark.slow)
