#!/usr/bin/env python3
"""
Tests for UI functionality.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.ui.app import ExtractionApp
from src.core.base_classes import ExtractionResult, TokenUsage


class TestExtractionApp:
    """Test Gradio application functionality."""
    
    @pytest.fixture
    def temp_output_dir(self):
        """Create temporary output directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_extraction_result(self):
        """Create mock extraction result."""
        return ExtractionResult(
            success=True,
            data=[
                {
                    "equipment_name": "Main Engine",
                    "part_name": "Oil Filter",
                    "part_number": "OF-123",
                    "quantity": 2
                },
                {
                    "equipment_name": "Generator",
                    "part_name": "Air Filter",
                    "part_number": "AF-456",
                    "quantity": 1
                }
            ],
            extraction_method="llm_extraction",
            total_chunks=5,
            token_usage=TokenUsage(
                input_tokens=1000,
                output_tokens=200,
                total_tokens=1200,
                estimated_cost_usd=0.024
            ),
            metadata={"processing_time": 15.5}
        )
    
    def test_app_initialization(self, temp_output_dir):
        """Test app initialization."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            app = ExtractionApp()
            
            assert app.output_dir == temp_output_dir
            assert app.current_chunks == []
            assert app.current_result is None
    
    @patch('src.ui.app.SupabaseRetriever')
    def test_get_available_files(self, mock_retriever_class, temp_output_dir):
        """Test getting available files."""
        # Mock retriever
        mock_retriever = Mock()
        mock_retriever.get_available_files.return_value = ["file1.pdf", "file2.pdf"]
        mock_retriever_class.return_value = mock_retriever
        
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            mock_settings.database.supabase_url = "test_url"
            mock_settings.database.supabase_key = "test_key"
            
            app = ExtractionApp()
            files = app._get_available_files()
            
            assert isinstance(files, list)
            assert len(files) == 2
            assert "file1.pdf" in files
            assert "file2.pdf" in files
    
    @patch('src.ui.app.SupabaseRetriever')
    def test_get_available_files_error(self, mock_retriever_class, temp_output_dir):
        """Test getting available files with error."""
        # Mock retriever to raise exception
        mock_retriever = Mock()
        mock_retriever.get_available_files.side_effect = Exception("Connection failed")
        mock_retriever_class.return_value = mock_retriever
        
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            mock_settings.database.supabase_url = "test_url"
            mock_settings.database.supabase_key = "test_key"
            
            app = ExtractionApp()
            files = app._get_available_files()
            
            assert files == []
    
    def test_get_available_models(self, temp_output_dir):
        """Test getting available models."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            mock_settings.get_available_models.return_value = {
                "openai": ["gpt-4", "gpt-3.5-turbo"],
                "anthropic": ["claude-3"]
            }
            
            app = ExtractionApp()
            models = app._get_available_models()
            
            assert isinstance(models, list)
            assert "gpt-4" in models
            assert "gpt-3.5-turbo" in models
            assert "claude-3" in models
    
    @patch('src.ui.app.SupabaseRetriever')
    @patch('src.ui.app.ChunkProcessor')
    def test_retrieve_chunks(self, mock_processor_class, mock_retriever_class, temp_output_dir):
        """Test chunk retrieval."""
        # Mock retriever
        mock_retriever = Mock()
        mock_chunks = [
            Mock(text="Sample chunk 1", page_number=1),
            Mock(text="Sample chunk 2", page_number=2)
        ]
        mock_retriever.search_multiple_queries.return_value = mock_chunks
        mock_retriever_class.return_value = mock_retriever
        
        # Mock processor
        mock_processor = Mock()
        mock_processor.aggregate_chunks.return_value = mock_chunks
        mock_processor.get_chunk_statistics.return_value = {
            "total_chunks": 2,
            "unique_files": 1
        }
        mock_processor_class.return_value = mock_processor
        
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            mock_settings.database.supabase_url = "test_url"
            mock_settings.database.supabase_key = "test_key"
            
            with patch('src.ui.app.ConfigManager') as mock_config_manager:
                mock_config = Mock()
                mock_config.search_queries = ["query1", "query2"]
                mock_config_manager.return_value.get_extraction_config.return_value = mock_config
                
                app = ExtractionApp()
                result = app._retrieve_chunks("spare_parts", "test.pdf", 10)
                
                assert "Retrieved 2 chunks" in result
                assert len(app.current_chunks) == 2
    
    @patch('src.ui.app.LLMExtractor')
    def test_extract_data(self, mock_extractor_class, temp_output_dir, mock_extraction_result):
        """Test data extraction."""
        # Mock extractor
        mock_extractor = Mock()
        mock_extractor.validate_and_extract.return_value = mock_extraction_result
        mock_extractor_class.return_value = mock_extractor
        
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            with patch('src.ui.app.ConfigManager') as mock_config_manager:
                mock_config = Mock()
                mock_config.system_prompt = "Test prompt"
                mock_config.schema = {"type": "object"}
                mock_config_manager.return_value.get_extraction_config.return_value = mock_config
                
                app = ExtractionApp()
                app.current_chunks = [Mock(), Mock()]  # Mock chunks
                
                result = app._extract_data("spare_parts", "gpt-4")
                
                assert "Successfully extracted 2 items" in result
                assert app.current_result is not None
    
    def test_export_json(self, temp_output_dir, mock_extraction_result):
        """Test JSON export."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            app = ExtractionApp()
            app.current_result = mock_extraction_result
            
            file_path = app._export_json("test_export")
            
            assert file_path is not None
            assert Path(file_path).exists()
            
            # Verify content
            with open(file_path, 'r') as f:
                data = json.load(f)
                assert "data" in data
                assert "metadata" in data
                assert len(data["data"]) == 2
    
    def test_export_csv(self, temp_output_dir, mock_extraction_result):
        """Test CSV export."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            app = ExtractionApp()
            app.current_result = mock_extraction_result
            
            file_path = app._export_csv("test_export")
            
            assert file_path is not None
            assert Path(file_path).exists()
            
            # Verify content
            with open(file_path, 'r') as f:
                content = f.read()
                assert "equipment_name" in content
                assert "Main Engine" in content
                assert "Generator" in content
    
    def test_export_no_data(self, temp_output_dir):
        """Test export with no data."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            app = ExtractionApp()
            app.current_result = None
            
            json_path = app._export_json("test")
            csv_path = app._export_csv("test")
            
            assert json_path is None
            assert csv_path is None
    
    def test_format_chunks_for_display(self, temp_output_dir):
        """Test formatting chunks for display."""
        mock_chunks = [
            Mock(
                text_raw="Sample chunk content 1",
                page_number=1,
                file_name="test.pdf",
                similarity_score=0.9
            ),
            Mock(
                text_raw="Sample chunk content 2",
                page_number=2,
                file_name="test.pdf",
                similarity_score=0.8
            )
        ]
        
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            app = ExtractionApp()
            formatted = app._format_chunks_for_display(mock_chunks)
            
            assert isinstance(formatted, str)
            assert "## Chunk 1" in formatted
            assert "## Chunk 2" in formatted
            assert "Page: 1" in formatted
            assert "Page: 2" in formatted
            assert "Similarity: 0.90" in formatted
            assert "Similarity: 0.80" in formatted
    
    def test_format_results_for_display(self, temp_output_dir, mock_extraction_result):
        """Test formatting results for display."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            
            app = ExtractionApp()
            formatted = app._format_results_for_display(mock_extraction_result)
            
            assert isinstance(formatted, str)
            assert "## Extraction Results" in formatted
            assert "**Success:** ✅" in formatted
            assert "**Items Extracted:** 2" in formatted
            assert "**Token Usage:**" in formatted
            assert "Main Engine" in formatted
            assert "Generator" in formatted
    
    def test_get_system_status(self, temp_output_dir):
        """Test getting system status."""
        with patch('src.ui.app.settings') as mock_settings:
            mock_settings.output_dir = temp_output_dir
            mock_settings.validate_configuration.return_value = []
            
            with patch('src.ui.app.SupabaseRetriever') as mock_retriever_class:
                mock_retriever = Mock()
                mock_retriever.health_check.return_value = True
                mock_retriever_class.return_value = mock_retriever
                
                app = ExtractionApp()
                status = app._get_system_status()
                
                assert isinstance(status, str)
                assert "System Status" in status
                assert "✅" in status  # Should show success indicators


if __name__ == "__main__":
    pytest.main([__file__])
