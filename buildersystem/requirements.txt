# Core PDF processing dependencies
pdfplumber>=0.11.7
PyMuPDF>=1.26.1

# AI and embeddings
openai>=1.91.0

# Database and vector storage
supabase>=2.16.0
pgvector>=0.3.6
psycopg2-binary>=2.9.10

# Environment and configuration
python-dotenv>=1.1.1

# Testing dependencies
pytest>=8.4.1
pytest-asyncio>=1.0.0
pytest-mock>=3.14.1

# Web interface and data processing
gradio>=5.0.0
pandas>=2.0.0
openpyxl>=3.1.0

# AI model clients
mistralai>=1.0.0
anthropic>=0.40.0

# LangChain integrations
langchain>=0.3.0
langchain-openai>=0.2.0
langchain-mistralai>=0.2.0
langchain-anthropic>=0.2.0
langchain-core>=0.3.0

# Additional dependencies for new system
pydantic>=2.0.0
numpy>=1.24.0
requests>=2.31.0
tqdm>=4.65.0
python-dateutil>=2.8.0

# LangSmith integration (optional)
langsmith>=0.1.0

# Additional testing utilities
coverage>=7.0.0
pytest-cov>=4.0.0

# Development dependencies
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Additional utilities (already installed in environment)
Pillow>=11.2.1
numpy>=2.3.1