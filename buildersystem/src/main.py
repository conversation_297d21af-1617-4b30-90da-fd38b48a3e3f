#!/usr/bin/env python3
"""
Main application entry point for marine equipment data extraction system.

This module orchestrates all components and provides a clean interface
for running the extraction system.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import List, Optional

from .config.settings import settings
from .config.config_manager import config_manager
from .core.base_classes import ExtractionType
from .core.utils import setup_logging
from .ui.app import launch_app
from .retrieval.retrieval_service import RetrievalService
from .retrieval.supabase_retriever import SupabaseRetriever
from .extraction.extraction_service import ExtractionService, ExtractionMethod

logger = logging.getLogger(__name__)


def validate_system() -> List[str]:
    """
    Validate system configuration and dependencies.
    
    Returns:
        List of validation issues (empty if valid)
    """
    issues = []
    
    # Validate settings
    config_issues = settings.validate_configuration()
    issues.extend(config_issues)
    
    # Validate extraction configurations
    for extraction_type in ExtractionType:
        try:
            validation_issues = config_manager.validate_extraction_config(extraction_type)
            issues.extend(validation_issues)
        except Exception as e:
            issues.append(f"Failed to validate {extraction_type.value}: {e}")
    
    # Test database connection
    try:
        retriever = SupabaseRetriever()
        if not retriever.health_check():
            issues.append("Supabase database connection failed")
    except Exception as e:
        issues.append(f"Database connection error: {e}")
    
    return issues


def run_cli_extraction(file_name: str, extraction_type: str, model_name: Optional[str] = None,
                      max_chunks: int = 10, output_format: str = "json",
                      extraction_method: str = "auto") -> bool:
    """
    Run extraction from command line.
    
    Args:
        file_name: Name of the file to process
        extraction_type: Type of extraction
        model_name: Model to use (optional)
        max_chunks: Maximum chunks to process
        output_format: Output format (json or csv)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Initialize components
        retrieval_service = RetrievalService()
        extraction_service = ExtractionService(model_name=model_name)

        # Get extraction configuration
        extraction_enum = ExtractionType(extraction_type)
        config = config_manager.get_extraction_config(extraction_enum)

        logger.info(f"Starting CLI extraction: {file_name} -> {extraction_type}")

        # Retrieve chunks using shared service
        processed_chunks = retrieval_service.retrieve_chunks(
            file_name, config, max_chunks, use_search_queries=True
        )

        if not processed_chunks:
            logger.error(f"No chunks found for file: {file_name}")
            return False

        logger.info(f"Processing {len(processed_chunks)} chunks")
        
        # Determine extraction method
        if extraction_method == "auto":
            selected_method = extraction_service.get_recommended_method(processed_chunks)
            logger.info(f"Auto-selected {selected_method.value} extraction method")
        else:
            selected_method = ExtractionMethod(extraction_method)
            logger.info(f"Using specified {selected_method.value} extraction method")

        # Perform extraction
        result = extraction_service.extract(
            processed_chunks,
            config,
            method=selected_method
        )
        
        if not result.success:
            logger.error(f"Extraction failed: {result.error}")
            return False
        
        # Save results
        from datetime import datetime
        from .core.utils import save_to_json, save_to_csv
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if output_format.lower() == "csv":
            filename = f"{extraction_type}_{file_name}_{timestamp}.csv"
            filepath = settings.output_dir / filename
            save_to_csv(result.data, filepath)
        else:
            filename = f"{extraction_type}_{file_name}_{timestamp}.json"
            filepath = settings.output_dir / filename
            save_to_json(result.data, filepath)
        
        # Print summary
        token_info = ""
        if result.token_usage:
            token_info = f" (Tokens: {result.token_usage.total_tokens}, Cost: ${result.token_usage.estimated_cost_usd:.4f})"
        
        logger.info(f"✅ Extraction completed: {len(result.data)} items extracted{token_info}")
        logger.info(f"Results saved to: {filepath}")
        
        return True
        
    except Exception as e:
        logger.error(f"CLI extraction failed: {e}")
        return False


def list_available_files() -> None:
    """List available files in the database."""
    try:
        retriever = SupabaseRetriever()
        files = retriever.get_available_files()
        
        if files:
            print("Available files:")
            for i, file_name in enumerate(files, 1):
                print(f"  {i}. {file_name}")
        else:
            print("No files found in database")
            
    except Exception as e:
        logger.error(f"Failed to list files: {e}")


def list_extraction_types() -> None:
    """List available extraction types."""
    print("Available extraction types:")
    for extraction_type in ExtractionType:
        try:
            config = config_manager.get_extraction_config(extraction_type)
            print(f"  - {extraction_type.value}: {config.description or 'No description'}")
        except Exception as e:
            print(f"  - {extraction_type.value}: Error loading config - {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Marine Equipment Data Extraction System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Launch web interface
  python -m src.main

  # Run CLI extraction
  python -m src.main --cli --file "document.pdf" --type "spare_parts" --model "gpt-4o-mini"

  # List available files
  python -m src.main --list-files

  # Validate system
  python -m src.main --validate
        """
    )
    
    # Mode selection
    parser.add_argument("--cli", action="store_true", help="Run in CLI mode")
    parser.add_argument("--validate", action="store_true", help="Validate system configuration")
    parser.add_argument("--list-files", action="store_true", help="List available files")
    parser.add_argument("--list-types", action="store_true", help="List extraction types")
    
    # CLI extraction options
    parser.add_argument("--file", type=str, help="File name to process (CLI mode)")
    parser.add_argument("--type", type=str, help="Extraction type (CLI mode)")
    parser.add_argument("--model", type=str, help="Model name to use")
    parser.add_argument("--max-chunks", type=int, default=10, help="Maximum chunks to process")
    parser.add_argument("--extraction-method", choices=["batch", "incremental", "auto"],
                        default="auto", help="Extraction method: batch (all chunks at once), "
                        "incremental (one chunk at a time with memory), or auto (recommended)")
    parser.add_argument("--output", choices=["json", "csv"], default="json", help="Output format")
    
    # UI options
    parser.add_argument("--share", action="store_true", help="Share Gradio interface publicly")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    # Logging options
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="Logging level")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(log_level=args.log_level)
    
    try:
        # Handle different modes
        if args.validate:
            print("Validating system configuration...")
            issues = validate_system()
            if issues:
                print("❌ Validation failed:")
                for issue in issues:
                    print(f"  - {issue}")
                sys.exit(1)
            else:
                print("✅ System validation passed")
                sys.exit(0)
        
        elif args.list_files:
            list_available_files()
            sys.exit(0)
        
        elif args.list_types:
            list_extraction_types()
            sys.exit(0)
        
        elif args.cli:
            if not args.file or not args.type:
                parser.error("CLI mode requires --file and --type arguments")
            
            success = run_cli_extraction(
                args.file, args.type, args.model, args.max_chunks, args.output,
                getattr(args, 'extraction_method', 'auto')
            )
            sys.exit(0 if success else 1)
        
        else:
            # Default: Launch web interface
            print("🚀 Launching Marine Equipment Data Extraction System...")
            
            # Quick validation
            issues = validate_system()
            if issues:
                logger.warning("Configuration issues detected:")
                for issue in issues:
                    logger.warning(f"  - {issue}")
                print("⚠️  Some configuration issues detected. Check logs for details.")
            
            launch_app(share=args.share, debug=args.debug)
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
