#!/usr/bin/env python3
"""
Configuration manager for loading schemas, prompts, and search queries.

This module provides a centralized way to manage and load configuration
files from the data directory structure.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from ..core.base_classes import BaseConfigManager, ExtractionType, ConfigurationError
from .settings import settings

logger = logging.getLogger(__name__)


@dataclass
class ExtractionConfiguration:
    """Complete configuration for an extraction type."""
    name: str
    extraction_type: ExtractionType
    schema: Dict[str, Any]
    system_prompt: str
    search_queries: List[str]
    description: Optional[str] = None


class ConfigManager(BaseConfigManager):
    """Configuration manager implementation."""
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize configuration manager.
        
        Args:
            data_dir: Path to data directory (defaults to settings.data_dir)
        """
        self.data_dir = data_dir or settings.data_dir
        self.schemas_dir = self.data_dir / "schemas"
        self.prompts_dir = self.data_dir / "prompts"
        self.queries_dir = self.data_dir / "queries"
        
        # Validate directories exist
        self._validate_directories()
        
        # Cache for loaded configurations
        self._config_cache: Dict[str, ExtractionConfiguration] = {}
    
    def _validate_directories(self) -> None:
        """Validate that required directories exist."""
        required_dirs = [self.data_dir, self.schemas_dir, self.prompts_dir, self.queries_dir]
        
        for directory in required_dirs:
            if not directory.exists():
                raise ConfigurationError(f"Required directory does not exist: {directory}")
    
    def load_schema(self, extraction_type: ExtractionType) -> Dict[str, Any]:
        """
        Load JSON schema for extraction type.
        
        Args:
            extraction_type: Type of extraction
            
        Returns:
            JSON schema dictionary
            
        Raises:
            ConfigurationError: If schema file cannot be loaded
        """
        schema_file = self.schemas_dir / f"{extraction_type.value}.json"
        
        if not schema_file.exists():
            raise ConfigurationError(f"Schema file not found: {schema_file}")
        
        try:
            with open(schema_file, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            logger.debug(f"Loaded schema for {extraction_type.value}")
            return schema
            
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in schema file {schema_file}: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading schema file {schema_file}: {e}")
    
    def load_prompt(self, extraction_type: ExtractionType) -> str:
        """
        Load system prompt for extraction type.
        
        Args:
            extraction_type: Type of extraction
            
        Returns:
            System prompt text
            
        Raises:
            ConfigurationError: If prompt file cannot be loaded
        """
        prompt_file = self.prompts_dir / f"{extraction_type.value}.txt"
        
        if not prompt_file.exists():
            raise ConfigurationError(f"Prompt file not found: {prompt_file}")
        
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            
            if not prompt:
                raise ConfigurationError(f"Empty prompt file: {prompt_file}")
            
            logger.debug(f"Loaded prompt for {extraction_type.value}")
            return prompt
            
        except Exception as e:
            raise ConfigurationError(f"Error loading prompt file {prompt_file}: {e}")
    
    def load_queries(self, extraction_type: ExtractionType) -> List[str]:
        """
        Load search queries for extraction type.
        
        Args:
            extraction_type: Type of extraction
            
        Returns:
            List of search queries
            
        Raises:
            ConfigurationError: If queries file cannot be loaded
        """
        queries_file = self.queries_dir / f"{extraction_type.value}.txt"
        
        if not queries_file.exists():
            logger.warning(f"Queries file not found: {queries_file}")
            return []
        
        try:
            with open(queries_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content:
                logger.warning(f"Empty queries file: {queries_file}")
                return []
            
            # Split by lines and filter out empty lines
            queries = [line.strip() for line in content.split('\n') if line.strip()]
            
            logger.debug(f"Loaded {len(queries)} queries for {extraction_type.value}")
            return queries
            
        except Exception as e:
            logger.error(f"Error loading queries file {queries_file}: {e}")
            return []
    
    def get_available_extractions(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all available extraction configurations.
        
        Returns:
            Dictionary mapping extraction type names to configuration dictionaries
        """
        extractions = {}
        
        # Get all schema files
        schema_files = list(self.schemas_dir.glob("*.json"))
        
        for schema_file in schema_files:
            extraction_name = schema_file.stem
            
            try:
                # Try to create ExtractionType from filename
                extraction_type = ExtractionType(extraction_name)
                
                # Load configuration
                config = self.get_extraction_config(extraction_type)
                
                # Convert to dictionary format for backward compatibility
                extractions[extraction_name] = {
                    "name": config.name,
                    "schema": config.schema,
                    "system_prompt": config.system_prompt,
                    "search_queries": config.search_queries,
                    "description": config.description or f"Extract {extraction_name.replace('_', ' ')} data"
                }
                
            except ValueError:
                logger.warning(f"Unknown extraction type: {extraction_name}")
                continue
            except Exception as e:
                logger.error(f"Error loading configuration for {extraction_name}: {e}")
                continue
        
        return extractions
    
    def get_extraction_config(self, extraction_type: ExtractionType) -> ExtractionConfiguration:
        """
        Get complete configuration for an extraction type.
        
        Args:
            extraction_type: Type of extraction
            
        Returns:
            Complete extraction configuration
            
        Raises:
            ConfigurationError: If configuration cannot be loaded
        """
        cache_key = extraction_type.value
        
        # Return cached configuration if available
        if cache_key in self._config_cache:
            return self._config_cache[cache_key]
        
        try:
            # Load all components
            schema = self.load_schema(extraction_type)
            system_prompt = self.load_prompt(extraction_type)
            search_queries = self.load_queries(extraction_type)
            
            # Create configuration
            config = ExtractionConfiguration(
                name=extraction_type.value.replace('_', ' ').title(),
                extraction_type=extraction_type,
                schema=schema,
                system_prompt=system_prompt,
                search_queries=search_queries,
                description=f"Extract {extraction_type.value.replace('_', ' ')} data from marine equipment PDFs"
            )
            
            # Cache the configuration
            self._config_cache[cache_key] = config
            
            logger.info(f"Loaded configuration for {extraction_type.value}")
            return config
            
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration for {extraction_type.value}: {e}")
    
    def validate_extraction_config(self, extraction_type: ExtractionType) -> List[str]:
        """
        Validate configuration for an extraction type.
        
        Args:
            extraction_type: Type of extraction
            
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        try:
            config = self.get_extraction_config(extraction_type)
            
            # Validate schema
            if not config.schema:
                issues.append(f"Empty schema for {extraction_type.value}")
            elif not isinstance(config.schema, dict):
                issues.append(f"Invalid schema format for {extraction_type.value}")
            
            # Validate prompt
            if not config.system_prompt:
                issues.append(f"Empty system prompt for {extraction_type.value}")
            elif len(config.system_prompt) < 50:
                issues.append(f"System prompt too short for {extraction_type.value}")
            
            # Validate queries (optional but warn if missing)
            if not config.search_queries:
                issues.append(f"No search queries for {extraction_type.value} (optional but recommended)")
            
        except ConfigurationError as e:
            issues.append(str(e))
        
        return issues
    
    def reload_config(self, extraction_type: Optional[ExtractionType] = None) -> None:
        """
        Reload configuration from files.
        
        Args:
            extraction_type: Specific type to reload (None to reload all)
        """
        if extraction_type:
            # Reload specific configuration
            cache_key = extraction_type.value
            if cache_key in self._config_cache:
                del self._config_cache[cache_key]
            logger.info(f"Reloaded configuration for {extraction_type.value}")
        else:
            # Reload all configurations
            self._config_cache.clear()
            logger.info("Reloaded all configurations")


# Global config manager instance
config_manager = ConfigManager()
