#!/usr/bin/env python3
"""
Extraction service that supports both batch and incremental processing.

This service provides a unified interface for different extraction methods,
allowing users to choose between batch processing (all chunks at once) or
incremental processing (one chunk at a time with memory).
"""

import logging
from typing import List, Optional, Dict, Any
from enum import Enum

from ..core.base_classes import ChunkData, ExtractionResult, ExtractionType
from ..config.config_manager import ExtractionConfiguration
from .extractor import LLMExtractor
from .incremental_extractor import IncrementalExtractor

logger = logging.getLogger(__name__)


class ExtractionMethod(Enum):
    """Available extraction methods."""
    BATCH = "batch"
    INCREMENTAL = "incremental"


class ExtractionService:
    """Service for managing different extraction methods."""
    
    def __init__(self, model_name: Optional[str] = None, temperature: float = 0.1):
        """
        Initialize extraction service.
        
        Args:
            model_name: Name of the model to use
            temperature: Temperature for generation
        """
        self.model_name = model_name
        self.temperature = temperature
        
        # Initialize extractors
        self.batch_extractor = LLMExtractor(model_name, temperature)
        self.incremental_extractor = IncrementalExtractor(model_name, temperature)
        
        logger.info(f"Initialized extraction service with model: {model_name or 'default'}")
    
    def extract(self, 
                chunks: List[ChunkData], 
                config: ExtractionConfiguration,
                method: ExtractionMethod = ExtractionMethod.INCREMENTAL) -> ExtractionResult:
        """
        Extract structured data using the specified method.
        
        Args:
            chunks: List of chunks to process
            config: Extraction configuration
            method: Extraction method to use
            
        Returns:
            Extraction result
        """
        if not chunks:
            return ExtractionResult(
                success=False,
                error="No chunks provided for extraction",
                extraction_method=method.value
            )
        
        logger.info(f"Starting {method.value} extraction with {len(chunks)} chunks")
        
        try:
            if method == ExtractionMethod.BATCH:
                return self._extract_batch(chunks, config)
            elif method == ExtractionMethod.INCREMENTAL:
                return self._extract_incremental(chunks, config)
            else:
                raise ValueError(f"Unsupported extraction method: {method}")
                
        except Exception as e:
            logger.error(f"Extraction failed with method {method.value}: {e}")
            return ExtractionResult(
                success=False,
                error=str(e),
                extraction_method=method.value,
                total_chunks=len(chunks)
            )
    
    def _extract_batch(self, chunks: List[ChunkData], config: ExtractionConfiguration) -> ExtractionResult:
        """Extract using batch method (all chunks at once)."""
        logger.info("Using batch extraction method")
        
        return self.batch_extractor.extract(
            chunks=chunks,
            extraction_type=config.extraction_type,
            system_prompt=config.system_prompt,
            schema=config.schema
        )
    
    def _extract_incremental(self, chunks: List[ChunkData], config: ExtractionConfiguration) -> ExtractionResult:
        """Extract using incremental method (one chunk at a time with memory)."""
        logger.info("Using incremental extraction method with memory")
        
        return self.incremental_extractor.extract(
            chunks=chunks,
            extraction_type=config.extraction_type,
            system_prompt=config.system_prompt,
            schema=config.schema
        )
    
    def get_recommended_method(self, chunks: List[ChunkData]) -> ExtractionMethod:
        """
        Get recommended extraction method based on chunk characteristics.
        
        Args:
            chunks: List of chunks to analyze
            
        Returns:
            Recommended extraction method
        """
        if not chunks:
            return ExtractionMethod.BATCH
        
        # Calculate total content size
        total_chars = sum(len(chunk.text_raw) for chunk in chunks)
        avg_chunk_size = total_chars / len(chunks)
        
        # Recommend incremental for large documents or many chunks
        if len(chunks) > 10 or total_chars > 50000 or avg_chunk_size > 5000:
            logger.info(f"Recommending incremental method: {len(chunks)} chunks, {total_chars} chars")
            return ExtractionMethod.INCREMENTAL
        else:
            logger.info(f"Recommending batch method: {len(chunks)} chunks, {total_chars} chars")
            return ExtractionMethod.BATCH
    
    def get_method_info(self, method: ExtractionMethod) -> Dict[str, Any]:
        """
        Get information about an extraction method.
        
        Args:
            method: Extraction method
            
        Returns:
            Method information
        """
        if method == ExtractionMethod.BATCH:
            return {
                "name": "Batch Processing",
                "description": "Process all chunks together in a single request",
                "advantages": [
                    "Faster for small documents",
                    "Lower API call overhead",
                    "Simpler processing"
                ],
                "disadvantages": [
                    "May hit token limits with large documents",
                    "Less memory efficient",
                    "All-or-nothing processing"
                ],
                "recommended_for": "Small to medium documents (< 10 chunks, < 50k characters)"
            }
        elif method == ExtractionMethod.INCREMENTAL:
            return {
                "name": "Incremental Processing with Memory",
                "description": "Process chunks one at a time while maintaining context",
                "advantages": [
                    "Handles large documents efficiently",
                    "Memory-efficient processing",
                    "Maintains context across chunks",
                    "Resilient to individual chunk failures"
                ],
                "disadvantages": [
                    "More API calls (higher cost)",
                    "Slightly slower for small documents",
                    "More complex processing"
                ],
                "recommended_for": "Large documents (> 10 chunks, > 50k characters)"
            }
        else:
            return {"name": "Unknown", "description": "Unknown extraction method"}
    
    def estimate_processing_time(self, chunks: List[ChunkData], method: ExtractionMethod) -> Dict[str, Any]:
        """
        Estimate processing time and cost for extraction.
        
        Args:
            chunks: List of chunks to process
            method: Extraction method
            
        Returns:
            Processing estimates
        """
        if not chunks:
            return {"estimated_time_seconds": 0, "estimated_api_calls": 0, "estimated_tokens": 0}
        
        total_chars = sum(len(chunk.text_raw) for chunk in chunks)
        estimated_tokens = int(total_chars * 1.3)  # Rough token estimation
        
        if method == ExtractionMethod.BATCH:
            return {
                "estimated_time_seconds": 10 + (estimated_tokens / 1000) * 2,  # ~2s per 1k tokens
                "estimated_api_calls": 1,
                "estimated_tokens": estimated_tokens,
                "method": "batch"
            }
        elif method == ExtractionMethod.INCREMENTAL:
            return {
                "estimated_time_seconds": 5 + len(chunks) * 8,  # ~8s per chunk
                "estimated_api_calls": len(chunks),
                "estimated_tokens": estimated_tokens,
                "method": "incremental"
            }
        else:
            return {"estimated_time_seconds": 0, "estimated_api_calls": 0, "estimated_tokens": 0}


def create_extraction_service(model_name: Optional[str] = None, 
                            temperature: float = 0.1) -> ExtractionService:
    """
    Factory function to create an extraction service.
    
    Args:
        model_name: Name of the model to use
        temperature: Temperature for generation
        
    Returns:
        Configured extraction service
    """
    return ExtractionService(model_name, temperature)
