#!/usr/bin/env python3
"""
LLM-based data extractor using modern LangChain patterns.

This module provides structured data extraction from PDF chunks using
the latest LangChain structured output patterns with proper error handling.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_mistralai import ChatMistralAI
from langchain_anthropic import ChatAnthropic
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel

from ..core.base_classes import BaseExtractor, ChunkData, ExtractionResult, TokenUsage, ExtractionType, ExtractionError
from ..core.utils import clean_text_for_processing, calculate_cost_estimate
from ..config.settings import settings
from .models import get_model_for_extraction_type, validate_extraction_data, model_to_dict_list

logger = logging.getLogger(__name__)


class LLMExtractor(BaseExtractor):
    """LLM-based data extractor using LangChain structured output."""
    
    def __init__(self, model_name: Optional[str] = None, temperature: float = 0.1):
        """
        Initialize LLM extractor.
        
        Args:
            model_name: Name of the model to use (defaults to settings)
            temperature: Temperature for generation (defaults to 0.1)
        """
        self.model_name = model_name or settings.models.default_model
        self.temperature = temperature
        self.provider = settings.get_model_provider(self.model_name)
        
        # Initialize LLM
        self.llm = self._create_llm()
        
        logger.info(f"Initialized LLM extractor with model: {self.model_name}")
    
    def _create_llm(self):
        """Create LangChain LLM instance based on provider."""
        try:
            if self.provider == "openai":
                if not settings.models.openai_api_key:
                    raise ExtractionError("OpenAI API key not configured")
                return ChatOpenAI(
                    model=self.model_name,
                    temperature=self.temperature,
                    api_key=settings.models.openai_api_key
                )
            
            elif self.provider == "anthropic":
                if not settings.models.anthropic_api_key:
                    raise ExtractionError("Anthropic API key not configured")
                return ChatAnthropic(
                    model=self.model_name,
                    temperature=self.temperature,
                    api_key=settings.models.anthropic_api_key
                )
            
            elif self.provider == "mistral":
                if not settings.models.mistral_api_key:
                    raise ExtractionError("Mistral API key not configured")
                return ChatMistralAI(
                    model=self.model_name,
                    temperature=self.temperature,
                    api_key=settings.models.mistral_api_key
                )
            
            else:
                raise ExtractionError(f"Unsupported provider: {self.provider}")
                
        except Exception as e:
            raise ExtractionError(f"Failed to create LLM: {e}")
    
    def extract(self, chunks: List[ChunkData], extraction_type: ExtractionType, 
                system_prompt: str, schema: Dict[str, Any]) -> ExtractionResult:
        """
        Extract structured data from chunks using LangChain structured output.
        
        Args:
            chunks: List of chunks to process
            extraction_type: Type of extraction
            system_prompt: System prompt for extraction
            schema: JSON schema (not used with structured output)
            
        Returns:
            Extraction result with structured data
        """
        if not chunks:
            return ExtractionResult(
                success=False,
                error="No chunks provided for extraction",
                extraction_method="langchain_structured"
            )
        
        try:
            # Get the appropriate Pydantic model
            model_class = get_model_for_extraction_type(extraction_type.value)
            
            # Create structured LLM
            structured_llm = self.llm.with_structured_output(model_class)
            
            # Prepare context from chunks
            context = self._prepare_context(chunks)
            
            # Create prompt template
            prompt_template = ChatPromptTemplate.from_messages([
                ("system", self._create_system_message(system_prompt, extraction_type)),
                ("human", "Extract structured data from this PDF content:\n\n{context}")
            ])
            
            # Create extraction chain
            extraction_chain = prompt_template | structured_llm
            
            # Track token usage
            input_tokens = self._estimate_input_tokens(context, system_prompt)
            
            # Perform extraction
            logger.info(f"Starting extraction with {len(chunks)} chunks using {self.model_name}")
            
            result = extraction_chain.invoke({"context": context})
            
            # Estimate output tokens
            output_tokens = self._estimate_output_tokens(result)
            
            # Calculate token usage
            token_usage = TokenUsage(
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=input_tokens + output_tokens,
                estimated_cost_usd=calculate_cost_estimate(
                    TokenUsage(input_tokens, output_tokens, input_tokens + output_tokens),
                    self.model_name
                )
            )
            
            # Convert to dictionary list for export
            data_list = model_to_dict_list(result)
            
            logger.info(f"Extraction completed successfully: {len(data_list)} items extracted")
            
            return ExtractionResult(
                success=True,
                data=data_list,
                token_usage=token_usage,
                extraction_method="langchain_structured",
                total_chunks=len(chunks),
                metadata={
                    "model_name": self.model_name,
                    "provider": self.provider,
                    "extraction_type": extraction_type.value,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Extraction failed: {e}")
            return ExtractionResult(
                success=False,
                error=str(e),
                extraction_method="langchain_structured",
                total_chunks=len(chunks)
            )
    
    def _prepare_context(self, chunks: List[ChunkData]) -> str:
        """
        Prepare context from chunks for LLM processing.
        
        Args:
            chunks: List of chunks
            
        Returns:
            Formatted context string
        """
        context_parts = []
        
        for chunk in chunks:
            page_info = f"[Page {chunk.page_number}]"
            text = chunk.text_raw or chunk.text
            
            # Clean text for processing
            text_clean = clean_text_for_processing(text)
            
            if text_clean.strip():
                context_parts.append(f"{page_info}\n{text_clean}")
        
        return "\n\n---\n\n".join(context_parts)
    
    def _create_system_message(self, system_prompt: str, extraction_type: ExtractionType) -> str:
        """
        Create enhanced system message for extraction.

        Args:
            system_prompt: Base system prompt
            extraction_type: Type of extraction

        Returns:
            Enhanced system message
        """
        # Escape curly braces in system prompt to prevent LangChain template variable conflicts
        escaped_system_prompt = system_prompt.replace("{", "{{").replace("}", "}}")

        base_instructions = f"""You are an expert data extraction assistant specializing in marine equipment documentation.

Your task is to extract structured {extraction_type.value.replace('_', ' ')} data from PDF content.

CRITICAL REQUIREMENTS:
1. Extract data accurately and completely
2. Preserve the order of information as it appears in the document
3. Use null for missing fields rather than omitting them
4. Be precise and accurate - don't hallucinate information
5. Focus on the specific data type requested: {extraction_type.value.replace('_', ' ')}

EXTRACTION INSTRUCTIONS:
{escaped_system_prompt}

Remember to follow the schema exactly and maintain the document order."""

        return base_instructions
    
    def _estimate_input_tokens(self, context: str, system_prompt: str) -> int:
        """Estimate input tokens for the request."""
        total_text = context + system_prompt
        return len(total_text) // 4  # Rough approximation
    
    def _estimate_output_tokens(self, result: BaseModel) -> int:
        """Estimate output tokens from the result."""
        result_text = str(result.model_dump())
        return len(result_text) // 4  # Rough approximation
    
    def extract_with_fallback(self, chunks: List[ChunkData], extraction_type: ExtractionType,
                            system_prompt: str, schema: Dict[str, Any],
                            max_retries: int = 2) -> ExtractionResult:
        """
        Extract with fallback strategies for better reliability.
        
        Args:
            chunks: List of chunks to process
            extraction_type: Type of extraction
            system_prompt: System prompt for extraction
            schema: JSON schema
            max_retries: Maximum number of retries
            
        Returns:
            Extraction result
        """
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                result = self.extract(chunks, extraction_type, system_prompt, schema)
                
                if result.success:
                    if attempt > 0:
                        logger.info(f"Extraction succeeded on attempt {attempt + 1}")
                    return result
                else:
                    last_error = result.error
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"Extraction attempt {attempt + 1} failed: {e}")
        
        # If all attempts failed, try with smaller chunks
        if len(chunks) > 5:
            logger.info("Trying extraction with smaller chunk set")
            smaller_chunks = chunks[:5]  # Use first 5 chunks
            try:
                result = self.extract(smaller_chunks, extraction_type, system_prompt, schema)
                if result.success:
                    logger.info("Extraction succeeded with smaller chunk set")
                    return result
            except Exception as e:
                logger.error(f"Fallback extraction also failed: {e}")
        
        return ExtractionResult(
            success=False,
            error=f"Extraction failed after {max_retries + 1} attempts. Last error: {last_error}",
            extraction_method="langchain_structured_with_fallback",
            total_chunks=len(chunks)
        )
    
    def validate_and_extract(self, chunks: List[ChunkData], extraction_type: ExtractionType,
                           system_prompt: str, schema: Dict[str, Any]) -> ExtractionResult:
        """
        Extract and validate the results.
        
        Args:
            chunks: List of chunks to process
            extraction_type: Type of extraction
            system_prompt: System prompt for extraction
            schema: JSON schema
            
        Returns:
            Validated extraction result
        """
        result = self.extract_with_fallback(chunks, extraction_type, system_prompt, schema)
        
        if not result.success:
            return result
        
        try:
            # Additional validation can be added here
            if result.data and isinstance(result.data, list):
                # Check if we have meaningful data
                if len(result.data) == 0:
                    logger.warning("Extraction returned empty results")
                    result.metadata = result.metadata or {}
                    result.metadata["validation_warning"] = "Empty results"
                
                # Check for common issues
                non_empty_items = [item for item in result.data if any(v for v in item.values() if v)]
                if len(non_empty_items) < len(result.data) * 0.5:
                    logger.warning("Many extracted items appear to be empty")
                    result.metadata = result.metadata or {}
                    result.metadata["validation_warning"] = "Many empty items"
            
            logger.info("Extraction validation completed")
            return result
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            result.metadata = result.metadata or {}
            result.metadata["validation_error"] = str(e)
            return result
