#!/usr/bin/env python3
"""
Pydantic models for structured data extraction.

This module defines the Pydantic models used for structured output from LLMs,
following the latest LangChain patterns for structured data extraction.
"""

from typing import List, Optional, Union
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


# Spare Parts Extraction Models
class SparePart(BaseModel):
    """Individual spare part entry."""
    equipment_name: Optional[str] = Field(
        default=None, 
        description="Name of the equipment this spare part belongs to"
    )
    part_name: Optional[str] = Field(
        default=None, 
        description="Name or description of the spare part"
    )
    part_number: Optional[str] = Field(
        default=None, 
        description="Part number or catalog number"
    )
    drawing_number: Optional[str] = Field(
        default=None, 
        description="Technical drawing number or reference"
    )
    position_number: Optional[str] = Field(
        default=None, 
        description="Position number in assembly or drawing"
    )
    quantity: Optional[Union[int, str]] = Field(
        default=None, 
        description="Required quantity of the spare part"
    )
    units: Optional[str] = Field(
        default=None, 
        description="Unit of measurement (pcs, kg, m, etc.)"
    )
    materials: Optional[str] = Field(
        default=None, 
        description="Material specification or composition"
    )
    remarks: Optional[str] = Field(
        default=None, 
        description="Additional remarks or notes"
    )
    spare_part_title: Optional[str] = Field(
        default=None, 
        description="Title or category of the spare part"
    )
    pdf_reference: Optional[str] = Field(
        default=None, 
        description="Reference to source PDF page or section"
    )


class SparePartsList(BaseModel):
    """Collection of spare parts extracted from a document."""
    spare_parts: List[SparePart] = Field(
        description="List of spare parts found in the document"
    )


# Component Extraction Models
class Component(BaseModel):
    """Individual component entry with parent-child hierarchy."""
    equipment_name: Optional[str] = Field(
        default=None, 
        description="Name of the main equipment or system"
    )
    maker_name: Optional[str] = Field(
        default=None, 
        description="Manufacturer or maker of the component"
    )
    model: Optional[str] = Field(
        default=None, 
        description="Model number or designation"
    )
    serial_number: Optional[str] = Field(
        default=None, 
        description="Serial number of the component"
    )
    particulars: Optional[str] = Field(
        default=None, 
        description="Specific details or specifications"
    )
    motor_capacity: Optional[str] = Field(
        default=None, 
        description="Motor capacity or power rating"
    )
    quantity: Optional[Union[int, str]] = Field(
        default=None, 
        description="Number of components"
    )
    component_type: Optional[str] = Field(
        default=None, 
        description="Type or category of component (major mechanical/electrical equipment)"
    )
    pdf_reference: Optional[str] = Field(
        default=None, 
        description="Reference to source PDF page or section"
    )
    spare_pages: Optional[str] = Field(
        default=None, 
        description="Pages containing spare parts information"
    )
    job_pages: Optional[str] = Field(
        default=None, 
        description="Pages containing job/maintenance information"
    )


class ComponentsList(BaseModel):
    """Collection of components extracted from a document."""
    components: List[Component] = Field(
        description="List of components found in the document"
    )


# Job Extraction Models
class Job(BaseModel):
    """Individual job or maintenance task entry."""
    equipment_name: Optional[str] = Field(
        default=None, 
        description="Name of the equipment this job applies to"
    )
    job_body: Optional[str] = Field(
        default=None, 
        description="Main body or description of the job"
    )
    job_action: Optional[str] = Field(
        default=None, 
        description="Specific action to be performed"
    )
    frequency: Optional[str] = Field(
        default=None, 
        description="How often the job should be performed"
    )
    frequency_type: Optional[str] = Field(
        default=None, 
        description="Type of frequency (hours, days, months, etc.)"
    )
    job_procedure: Optional[str] = Field(
        default=None, 
        description="Detailed procedure for performing the job"
    )
    pdf_reference: Optional[str] = Field(
        default=None, 
        description="Reference to source PDF page or section"
    )


class JobsList(BaseModel):
    """Collection of jobs extracted from a document."""
    jobs: List[Job] = Field(
        description="List of jobs/maintenance tasks found in the document"
    )


# Union type for all extraction models
ExtractionModel = Union[SparePartsList, ComponentsList, JobsList]


def get_model_for_extraction_type(extraction_type: str) -> type[BaseModel]:
    """
    Get the appropriate Pydantic model for an extraction type.
    
    Args:
        extraction_type: Type of extraction (spare_parts, component_extraction, job_extraction)
        
    Returns:
        Pydantic model class
        
    Raises:
        ValueError: If extraction type is not supported
    """
    model_mapping = {
        "spare_parts": SparePartsList,
        "component_extraction": ComponentsList,
        "job_extraction": JobsList,
    }
    
    if extraction_type not in model_mapping:
        raise ValueError(f"Unsupported extraction type: {extraction_type}")
    
    return model_mapping[extraction_type]


def validate_extraction_data(data: dict, extraction_type: str) -> BaseModel:
    """
    Validate extraction data against the appropriate model.
    
    Args:
        data: Raw extraction data
        extraction_type: Type of extraction
        
    Returns:
        Validated Pydantic model instance
        
    Raises:
        ValueError: If data doesn't match the expected schema
    """
    model_class = get_model_for_extraction_type(extraction_type)
    
    try:
        return model_class(**data)
    except Exception as e:
        logger.error(f"Validation failed for {extraction_type}: {e}")
        raise ValueError(f"Data validation failed: {e}")


def model_to_dict_list(model: BaseModel) -> List[dict]:
    """
    Convert a Pydantic model to a list of dictionaries for export.
    
    Args:
        model: Pydantic model instance
        
    Returns:
        List of dictionaries suitable for CSV/Excel export
    """
    if isinstance(model, SparePartsList):
        return [part.model_dump() for part in model.spare_parts]
    elif isinstance(model, ComponentsList):
        return [component.model_dump() for component in model.components]
    elif isinstance(model, JobsList):
        return [job.model_dump() for job in model.jobs]
    else:
        raise ValueError(f"Unsupported model type: {type(model)}")


def get_model_schema(extraction_type: str) -> dict:
    """
    Get the JSON schema for an extraction type.
    
    Args:
        extraction_type: Type of extraction
        
    Returns:
        JSON schema dictionary
    """
    model_class = get_model_for_extraction_type(extraction_type)
    return model_class.model_json_schema()
