#!/usr/bin/env python3
"""
LangSmith integration for tracing and monitoring LLM operations.

This module provides LangSmith integration for tracking token usage,
tracing extraction operations, and monitoring system performance.
"""

import logging
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from contextlib import contextmanager

try:
    from langsmith import Client, traceable
    from langsmith.run_helpers import trace
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    # Create dummy decorators if Lang<PERSON>mith is not available
    def traceable(func):
        return func
    
    def trace(func):
        return func

from ..config.settings import settings
from .base_classes import TokenUsage, ExtractionResult

logger = logging.getLogger(__name__)


class LangSmithTracker:
    """LangSmith integration for tracking and tracing."""
    
    def __init__(self, project_name: str = "marine-equipment-extraction"):
        """
        Initialize LangSmith tracker.
        
        Args:
            project_name: Name of the LangSmith project
        """
        self.project_name = project_name
        self.client = None
        self.enabled = False
        
        # Initialize <PERSON>Smith if available and configured
        self._initialize_langsmith()
    
    def _initialize_langsmith(self) -> None:
        """Initialize LangSmith client if available."""
        if not LANGSMITH_AVAILABLE:
            logger.info("LangSmith not available - install with: pip install langsmith")
            return
        
        # Check for LangSmith configuration
        api_key = os.getenv("LANGCHAIN_API_KEY") or os.getenv("LANGSMITH_API_KEY")
        endpoint = os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com")
        
        if not api_key:
            logger.info("LangSmith API key not found - tracing disabled")
            return
        
        try:
            self.client = Client(api_url=endpoint, api_key=api_key)
            
            # Set environment variables for LangChain integration
            os.environ["LANGCHAIN_TRACING_V2"] = "true"
            os.environ["LANGCHAIN_PROJECT"] = self.project_name
            os.environ["LANGCHAIN_API_KEY"] = api_key
            os.environ["LANGCHAIN_ENDPOINT"] = endpoint
            
            self.enabled = True
            logger.info(f"LangSmith initialized successfully - Project: {self.project_name}")
            
        except Exception as e:
            logger.warning(f"Failed to initialize LangSmith: {e}")
    
    @contextmanager
    def trace_extraction(self, extraction_type: str, file_name: str, model_name: str):
        """
        Context manager for tracing extraction operations.
        
        Args:
            extraction_type: Type of extraction being performed
            file_name: Name of the file being processed
            model_name: Name of the model being used
        """
        if not self.enabled:
            yield None
            return
        
        run_name = f"extract_{extraction_type}_{file_name}"
        
        try:
            with trace(
                name=run_name,
                run_type="chain",
                inputs={
                    "extraction_type": extraction_type,
                    "file_name": file_name,
                    "model_name": model_name,
                    "timestamp": datetime.now().isoformat()
                }
            ) as run:
                yield run
                
        except Exception as e:
            logger.error(f"LangSmith tracing error: {e}")
            yield None
    
    def log_extraction_result(self, result: ExtractionResult, 
                            extraction_type: str, file_name: str) -> None:
        """
        Log extraction result to LangSmith.
        
        Args:
            result: Extraction result
            extraction_type: Type of extraction
            file_name: Name of the processed file
        """
        if not self.enabled or not self.client:
            return
        
        try:
            # Create run data
            run_data = {
                "name": f"extraction_{extraction_type}_{file_name}",
                "run_type": "llm",
                "inputs": {
                    "extraction_type": extraction_type,
                    "file_name": file_name,
                    "total_chunks": result.total_chunks
                },
                "outputs": {
                    "success": result.success,
                    "items_extracted": len(result.data) if result.data else 0,
                    "extraction_method": result.extraction_method
                },
                "start_time": datetime.now(),
                "end_time": datetime.now()
            }
            
            # Add token usage if available
            if result.token_usage:
                run_data["outputs"]["token_usage"] = {
                    "input_tokens": result.token_usage.input_tokens,
                    "output_tokens": result.token_usage.output_tokens,
                    "total_tokens": result.token_usage.total_tokens,
                    "estimated_cost_usd": result.token_usage.estimated_cost_usd
                }
            
            # Add error information if extraction failed
            if not result.success:
                run_data["outputs"]["error"] = result.error
            
            # Add metadata
            if result.metadata:
                run_data["outputs"]["metadata"] = result.metadata
            
            # Log to LangSmith
            self.client.create_run(**run_data)
            logger.debug(f"Logged extraction result to LangSmith: {run_data['name']}")
            
        except Exception as e:
            logger.error(f"Failed to log extraction result to LangSmith: {e}")
    
    def log_token_usage(self, token_usage: TokenUsage, operation: str, 
                       model_name: str, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log token usage to LangSmith.
        
        Args:
            token_usage: Token usage information
            operation: Name of the operation
            model_name: Name of the model used
            context: Additional context information
        """
        if not self.enabled or not self.client:
            return
        
        try:
            run_data = {
                "name": f"token_usage_{operation}",
                "run_type": "llm",
                "inputs": {
                    "operation": operation,
                    "model_name": model_name,
                    "context": context or {}
                },
                "outputs": {
                    "input_tokens": token_usage.input_tokens,
                    "output_tokens": token_usage.output_tokens,
                    "total_tokens": token_usage.total_tokens,
                    "estimated_cost_usd": token_usage.estimated_cost_usd
                },
                "start_time": datetime.now(),
                "end_time": datetime.now()
            }
            
            self.client.create_run(**run_data)
            logger.debug(f"Logged token usage to LangSmith: {operation}")
            
        except Exception as e:
            logger.error(f"Failed to log token usage to LangSmith: {e}")
    
    def get_project_stats(self) -> Optional[Dict[str, Any]]:
        """
        Get project statistics from LangSmith.
        
        Returns:
            Project statistics or None if not available
        """
        if not self.enabled or not self.client:
            return None
        
        try:
            # Get recent runs
            runs = list(self.client.list_runs(project_name=self.project_name, limit=100))
            
            if not runs:
                return {"total_runs": 0, "total_tokens": 0, "total_cost": 0.0}
            
            # Calculate statistics
            total_runs = len(runs)
            total_tokens = 0
            total_cost = 0.0
            successful_runs = 0
            
            for run in runs:
                if run.outputs:
                    if run.outputs.get("token_usage"):
                        token_info = run.outputs["token_usage"]
                        total_tokens += token_info.get("total_tokens", 0)
                        total_cost += token_info.get("estimated_cost_usd", 0.0)
                    
                    if run.outputs.get("success"):
                        successful_runs += 1
            
            return {
                "total_runs": total_runs,
                "successful_runs": successful_runs,
                "success_rate": successful_runs / total_runs if total_runs > 0 else 0,
                "total_tokens": total_tokens,
                "total_cost_usd": total_cost,
                "avg_tokens_per_run": total_tokens / total_runs if total_runs > 0 else 0,
                "avg_cost_per_run": total_cost / total_runs if total_runs > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get project stats from LangSmith: {e}")
            return None
    
    def create_dataset(self, name: str, description: str, 
                      examples: List[Dict[str, Any]]) -> Optional[str]:
        """
        Create a dataset in LangSmith for evaluation.
        
        Args:
            name: Dataset name
            description: Dataset description
            examples: List of example data
            
        Returns:
            Dataset ID or None if creation failed
        """
        if not self.enabled or not self.client:
            return None
        
        try:
            dataset = self.client.create_dataset(
                dataset_name=name,
                description=description
            )
            
            # Add examples to dataset
            for example in examples:
                self.client.create_example(
                    dataset_id=dataset.id,
                    inputs=example.get("inputs", {}),
                    outputs=example.get("outputs", {})
                )
            
            logger.info(f"Created LangSmith dataset: {name} with {len(examples)} examples")
            return str(dataset.id)
            
        except Exception as e:
            logger.error(f"Failed to create LangSmith dataset: {e}")
            return None


# Global LangSmith tracker instance
langsmith_tracker = LangSmithTracker()


def trace_extraction(extraction_type: str, file_name: str, model_name: str):
    """
    Decorator for tracing extraction operations.
    
    Args:
        extraction_type: Type of extraction
        file_name: Name of the file
        model_name: Name of the model
    """
    def decorator(func):
        if not langsmith_tracker.enabled:
            return func
        
        @traceable(name=f"extract_{extraction_type}")
        def wrapper(*args, **kwargs):
            with langsmith_tracker.trace_extraction(extraction_type, file_name, model_name):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_llm_call(operation: str, model_name: str):
    """
    Decorator for logging LLM calls.
    
    Args:
        operation: Name of the operation
        model_name: Name of the model
    """
    def decorator(func):
        if not langsmith_tracker.enabled:
            return func
        
        @traceable(name=f"llm_call_{operation}")
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # Extract token usage if available in result
            if hasattr(result, 'token_usage') and result.token_usage:
                langsmith_tracker.log_token_usage(
                    result.token_usage, operation, model_name
                )
            
            return result
        
        return wrapper
    return decorator
