#!/usr/bin/env python3
"""
Base classes and interfaces for the structured data extraction system.

This module provides abstract base classes and common interfaces that define
the contract for different components of the system.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ExtractionType(Enum):
    """Supported extraction types."""
    SPARE_PARTS = "spare_parts"
    COMPONENT_EXTRACTION = "component_extraction"
    JOB_EXTRACTION = "job_extraction"


class ModelProvider(Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    MISTRAL = "mistral"


@dataclass
class TokenUsage:
    """Token usage tracking."""
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    estimated_cost_usd: float = 0.0


@dataclass
class ExtractionResult:
    """Result of a data extraction operation."""
    success: bool
    data: Optional[Union[List[Dict], Dict]] = None
    error: Optional[str] = None
    token_usage: Optional[TokenUsage] = None
    extraction_method: str = "unknown"
    total_chunks: int = 0
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ChunkData:
    """Represents a chunk of PDF content."""
    text: str
    text_raw: str
    page_number: int
    file_name: str
    metadata: Optional[Dict[str, Any]] = None
    similarity_score: Optional[float] = None


class BaseRetriever(ABC):
    """Abstract base class for document retrievers."""
    
    @abstractmethod
    def get_available_files(self) -> List[str]:
        """Get list of available files in the database."""
        pass
    
    @abstractmethod
    def semantic_search(self, query: str, file_name: str, max_chunks: int = 10) -> List[ChunkData]:
        """Perform semantic search for relevant chunks."""
        pass
    
    @abstractmethod
    def get_file_pages_ordered(self, file_name: str) -> List[ChunkData]:
        """Get all pages from a file in ascending order."""
        pass


class BaseExtractor(ABC):
    """Abstract base class for data extractors."""
    
    @abstractmethod
    def extract(self, chunks: List[ChunkData], extraction_type: ExtractionType, 
                system_prompt: str, schema: Dict[str, Any]) -> ExtractionResult:
        """Extract structured data from chunks."""
        pass


class BaseChunkProcessor(ABC):
    """Abstract base class for chunk processors."""
    
    @abstractmethod
    def filter_chunks(self, chunks: List[ChunkData], criteria: Dict[str, Any]) -> List[ChunkData]:
        """Filter chunks based on criteria."""
        pass
    
    @abstractmethod
    def deduplicate_chunks(self, chunks: List[ChunkData]) -> List[ChunkData]:
        """Remove duplicate chunks."""
        pass
    
    @abstractmethod
    def aggregate_chunks(self, chunk_lists: List[List[ChunkData]]) -> List[ChunkData]:
        """Aggregate multiple chunk lists."""
        pass


class BaseConfigManager(ABC):
    """Abstract base class for configuration management."""
    
    @abstractmethod
    def load_schema(self, extraction_type: ExtractionType) -> Dict[str, Any]:
        """Load JSON schema for extraction type."""
        pass
    
    @abstractmethod
    def load_prompt(self, extraction_type: ExtractionType) -> str:
        """Load system prompt for extraction type."""
        pass
    
    @abstractmethod
    def load_queries(self, extraction_type: ExtractionType) -> List[str]:
        """Load search queries for extraction type."""
        pass
    
    @abstractmethod
    def get_available_extractions(self) -> Dict[str, Dict[str, Any]]:
        """Get all available extraction configurations."""
        pass


class BaseExporter(ABC):
    """Abstract base class for data exporters."""
    
    @abstractmethod
    def export_json(self, data: Union[List[Dict], Dict], filename: str) -> str:
        """Export data to JSON format."""
        pass
    
    @abstractmethod
    def export_csv(self, data: List[Dict], filename: str) -> str:
        """Export data to CSV format."""
        pass
    
    @abstractmethod
    def export_excel(self, data: List[Dict], filename: str) -> str:
        """Export data to Excel format."""
        pass


class SystemError(Exception):
    """Base exception for system errors."""
    pass


class ConfigurationError(SystemError):
    """Raised when there's a configuration issue."""
    pass


class ExtractionError(SystemError):
    """Raised when extraction fails."""
    pass


class RetrievalError(SystemError):
    """Raised when retrieval fails."""
    pass


class ValidationError(SystemError):
    """Raised when validation fails."""
    pass
