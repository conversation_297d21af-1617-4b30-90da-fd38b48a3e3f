#!/usr/bin/env python3
"""
Comprehensive error handling and recovery mechanisms.

This module provides centralized error handling, recovery strategies,
and detailed error reporting for the extraction system.
"""

import logging
import traceback
from typing import Any, Dict, List, Optional, Callable, Union
from functools import wraps
from datetime import datetime
from pathlib import Path

from .base_classes import (
    ExtractionError, RetrievalError, ConfigurationError, 
    ValidationError, ProcessingError
)

logger = logging.getLogger(__name__)


class ErrorHandler:
    """Centralized error handler with recovery strategies."""
    
    def __init__(self, log_errors: bool = True, save_error_logs: bool = True):
        """
        Initialize error handler.
        
        Args:
            log_errors: Whether to log errors
            save_error_logs: Whether to save error logs to file
        """
        self.log_errors = log_errors
        self.save_error_logs = save_error_logs
        self.error_count = 0
        self.error_history: List[Dict[str, Any]] = []
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None,
                    recovery_strategy: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Handle an error with optional recovery strategy.
        
        Args:
            error: The exception that occurred
            context: Additional context information
            recovery_strategy: Optional recovery function
            
        Returns:
            Error information dictionary
        """
        self.error_count += 1
        
        # Create error info
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_id": f"ERR_{self.error_count:04d}",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "context": context or {},
            "recovery_attempted": False,
            "recovery_successful": False
        }
        
        # Log error
        if self.log_errors:
            self._log_error(error_info)
        
        # Attempt recovery if strategy provided
        if recovery_strategy:
            try:
                error_info["recovery_attempted"] = True
                recovery_result = recovery_strategy(error, context)
                error_info["recovery_successful"] = True
                error_info["recovery_result"] = recovery_result
                logger.info(f"Recovery successful for error {error_info['error_id']}")
            except Exception as recovery_error:
                error_info["recovery_error"] = str(recovery_error)
                logger.error(f"Recovery failed for error {error_info['error_id']}: {recovery_error}")
        
        # Save to history
        self.error_history.append(error_info)
        
        # Save to file if enabled
        if self.save_error_logs:
            self._save_error_log(error_info)
        
        return error_info
    
    def _log_error(self, error_info: Dict[str, Any]) -> None:
        """Log error information."""
        logger.error(f"Error {error_info['error_id']}: {error_info['error_type']} - {error_info['error_message']}")
        
        if error_info.get("context"):
            logger.error(f"Context: {error_info['context']}")
        
        # Log full traceback at debug level
        logger.debug(f"Full traceback for {error_info['error_id']}:\n{error_info['traceback']}")
    
    def _save_error_log(self, error_info: Dict[str, Any]) -> None:
        """Save error log to file."""
        try:
            from ..config.settings import settings
            
            error_log_dir = settings.logs_dir / "errors"
            error_log_dir.mkdir(exist_ok=True)
            
            log_file = error_log_dir / f"error_{error_info['error_id']}.json"
            
            import json
            with open(log_file, 'w') as f:
                json.dump(error_info, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Failed to save error log: {e}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors."""
        if not self.error_history:
            return {"total_errors": 0, "error_types": {}}
        
        error_types = {}
        for error in self.error_history:
            error_type = error["error_type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total_errors": len(self.error_history),
            "error_types": error_types,
            "recent_errors": self.error_history[-5:],  # Last 5 errors
            "recovery_success_rate": self._calculate_recovery_rate()
        }
    
    def _calculate_recovery_rate(self) -> float:
        """Calculate recovery success rate."""
        recovery_attempts = [e for e in self.error_history if e.get("recovery_attempted")]
        if not recovery_attempts:
            return 0.0
        
        successful_recoveries = [e for e in recovery_attempts if e.get("recovery_successful")]
        return len(successful_recoveries) / len(recovery_attempts)


# Global error handler instance
error_handler = ErrorHandler()


def with_error_handling(recovery_strategy: Optional[Callable] = None,
                       reraise: bool = False,
                       default_return: Any = None):
    """
    Decorator for automatic error handling.
    
    Args:
        recovery_strategy: Optional recovery function
        reraise: Whether to reraise the exception after handling
        default_return: Default return value if error occurs
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],  # Truncate long args
                    "kwargs": str(kwargs)[:200]
                }
                
                error_info = error_handler.handle_error(e, context, recovery_strategy)
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, **kwargs) -> Dict[str, Any]:
    """
    Safely execute a function with comprehensive error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        **kwargs: Function keyword arguments
        
    Returns:
        Dictionary with execution result and error information
    """
    result = {
        "success": False,
        "result": None,
        "error": None,
        "execution_time": None
    }
    
    start_time = datetime.now()
    
    try:
        result["result"] = func(*args, **kwargs)
        result["success"] = True
        
    except Exception as e:
        context = {
            "function": func.__name__ if hasattr(func, '__name__') else str(func),
            "args": str(args)[:200],
            "kwargs": str(kwargs)[:200]
        }
        
        error_info = error_handler.handle_error(e, context)
        result["error"] = error_info
        
    finally:
        end_time = datetime.now()
        result["execution_time"] = (end_time - start_time).total_seconds()
    
    return result


def validate_input(data: Any, validation_rules: Dict[str, Any]) -> List[str]:
    """
    Validate input data against rules.
    
    Args:
        data: Data to validate
        validation_rules: Validation rules dictionary
        
    Returns:
        List of validation errors (empty if valid)
    """
    errors = []
    
    try:
        # Required fields
        if "required_fields" in validation_rules and isinstance(data, dict):
            for field in validation_rules["required_fields"]:
                if field not in data or data[field] is None:
                    errors.append(f"Required field missing: {field}")
        
        # Type validation
        if "type" in validation_rules:
            expected_type = validation_rules["type"]
            if not isinstance(data, expected_type):
                errors.append(f"Expected type {expected_type.__name__}, got {type(data).__name__}")
        
        # Length validation
        if "min_length" in validation_rules:
            if hasattr(data, '__len__') and len(data) < validation_rules["min_length"]:
                errors.append(f"Length {len(data)} is less than minimum {validation_rules['min_length']}")
        
        if "max_length" in validation_rules:
            if hasattr(data, '__len__') and len(data) > validation_rules["max_length"]:
                errors.append(f"Length {len(data)} exceeds maximum {validation_rules['max_length']}")
        
        # Custom validation function
        if "custom_validator" in validation_rules:
            try:
                custom_errors = validation_rules["custom_validator"](data)
                if custom_errors:
                    errors.extend(custom_errors)
            except Exception as e:
                errors.append(f"Custom validation failed: {e}")
    
    except Exception as e:
        errors.append(f"Validation error: {e}")
    
    return errors


def create_recovery_strategy(strategy_type: str) -> Optional[Callable]:
    """
    Create a recovery strategy function.
    
    Args:
        strategy_type: Type of recovery strategy
        
    Returns:
        Recovery function or None
    """
    def retry_strategy(error: Exception, context: Dict[str, Any]) -> Any:
        """Retry the operation with exponential backoff."""
        import time
        
        max_retries = 3
        base_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                time.sleep(base_delay * (2 ** attempt))
                # This would need to be customized based on the specific operation
                logger.info(f"Retry attempt {attempt + 1} for {context.get('function', 'unknown')}")
                return "retry_attempted"
            except Exception as retry_error:
                if attempt == max_retries - 1:
                    raise retry_error
        
        return None
    
    def fallback_strategy(error: Exception, context: Dict[str, Any]) -> Any:
        """Use fallback values or methods."""
        logger.info(f"Using fallback strategy for {context.get('function', 'unknown')}")
        return "fallback_used"
    
    strategies = {
        "retry": retry_strategy,
        "fallback": fallback_strategy
    }
    
    return strategies.get(strategy_type)
