#!/usr/bin/env python3
"""
Utility functions for the structured data extraction system.

This module provides common utility functions used across the system.
"""

import json
import os
import re
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import asdict

from .base_classes import TokenUsage, ChunkData

logger = logging.getLogger(__name__)


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> None:
    """
    Setup comprehensive logging configuration with error handling.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
    """
    try:
        level = getattr(logging, log_level.upper(), logging.INFO)

        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(level)

        # Clear existing handlers
        root_logger.handlers.clear()

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)

        # File handler (if specified)
        if log_file:
            try:
                file_handler = logging.FileHandler(log_file)
                file_handler.setLevel(logging.DEBUG)  # Always log debug to file
                file_handler.setFormatter(detailed_formatter)
                root_logger.addHandler(file_handler)
            except Exception as e:
                print(f"Warning: Failed to setup file logging: {e}")

        # Set specific logger levels to reduce noise
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("openai").setLevel(logging.WARNING)
        logging.getLogger("anthropic").setLevel(logging.WARNING)
        logging.getLogger("gradio").setLevel(logging.WARNING)
        logging.getLogger("supabase").setLevel(logging.WARNING)

    except Exception as e:
        # Fallback to basic logging if setup fails
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        logging.error(f"Failed to setup advanced logging, using basic config: {e}")
    



def count_tokens(text: str) -> int:
    """
    Estimate token count for text.
    
    This is a rough approximation: 1 token ≈ 4 characters.
    For more accurate counting, use the specific tokenizer for your model.
    """
    return len(text) // 4 if text else 0


def clean_text_for_processing(text: str) -> str:
    """
    Clean text for LLM processing by removing base64 images and normalizing whitespace.
    
    Args:
        text: Raw text content
        
    Returns:
        Cleaned text suitable for LLM processing
    """
    if not text:
        return ""
    
    # Remove base64 images
    text_clean = re.sub(r'!\[.*?\]\(data:image/[^)]+\)', '[IMAGE]', text)
    
    # Normalize whitespace
    text_clean = re.sub(r'\s+', ' ', text_clean).strip()
    
    return text_clean


def validate_json_schema(schema_text: str) -> tuple[bool, str, Dict]:
    """
    Validate JSON schema format.
    
    Args:
        schema_text: JSON schema as string
        
    Returns:
        Tuple of (is_valid, message, parsed_schema)
    """
    try:
        schema = json.loads(schema_text)
        return True, "✅ Valid JSON schema", schema
    except json.JSONDecodeError as e:
        return False, f"❌ Invalid JSON: {e}", {}


def ensure_directory_exists(path: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path
        
    Returns:
        Path object for the directory
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj


def generate_timestamp() -> str:
    """Generate a timestamp string for file naming."""
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def safe_filename(filename: str) -> str:
    """
    Create a safe filename by removing/replacing problematic characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Safe filename
    """
    # Remove or replace problematic characters
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    safe_name = re.sub(r'[^\w\-_\.]', '_', safe_name)
    safe_name = re.sub(r'_+', '_', safe_name)  # Replace multiple underscores with single
    return safe_name.strip('_')


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def calculate_cost_estimate(token_usage: TokenUsage, model_name: str) -> float:
    """
    Calculate estimated cost based on token usage and model.
    
    Args:
        token_usage: Token usage information
        model_name: Name of the model used
        
    Returns:
        Estimated cost in USD
    """
    # Pricing per 1K tokens (as of 2024)
    pricing = {
        "gpt-4o": {"input": 0.005, "output": 0.015},
        "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
        "claude-3-opus": {"input": 0.015, "output": 0.075},
        "claude-3-sonnet": {"input": 0.003, "output": 0.015},
        "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
        "mistral-large": {"input": 0.008, "output": 0.024},
        "mistral-medium": {"input": 0.0027, "output": 0.0081},
    }
    
    # Default pricing if model not found
    default_pricing = {"input": 0.001, "output": 0.003}
    
    model_pricing = pricing.get(model_name.lower(), default_pricing)
    
    input_cost = (token_usage.input_tokens / 1000) * model_pricing["input"]
    output_cost = (token_usage.output_tokens / 1000) * model_pricing["output"]
    
    return input_cost + output_cost


def chunks_to_dict(chunks: List[ChunkData]) -> List[Dict[str, Any]]:
    """
    Convert ChunkData objects to dictionaries for serialization.
    
    Args:
        chunks: List of ChunkData objects
        
    Returns:
        List of dictionaries
    """
    return [asdict(chunk) for chunk in chunks]


def dict_to_chunks(chunk_dicts: List[Dict[str, Any]]) -> List[ChunkData]:
    """
    Convert dictionaries to ChunkData objects.
    
    Args:
        chunk_dicts: List of chunk dictionaries
        
    Returns:
        List of ChunkData objects
    """
    chunks = []
    for chunk_dict in chunk_dicts:
        chunks.append(ChunkData(**chunk_dict))
    return chunks


def merge_token_usage(usages: List[TokenUsage]) -> TokenUsage:
    """
    Merge multiple TokenUsage objects into one.
    
    Args:
        usages: List of TokenUsage objects
        
    Returns:
        Merged TokenUsage object
    """
    total_input = sum(usage.input_tokens for usage in usages)
    total_output = sum(usage.output_tokens for usage in usages)
    total_cost = sum(usage.estimated_cost_usd for usage in usages)
    
    return TokenUsage(
        input_tokens=total_input,
        output_tokens=total_output,
        total_tokens=total_input + total_output,
        estimated_cost_usd=total_cost
    )


def save_to_json(data: Any, file_path: str) -> bool:
    """
    Save data to JSON file.

    Args:
        data: Data to save
        file_path: Path to save file

    Returns:
        True if successful, False otherwise
    """
    try:
        from pathlib import Path

        # Ensure directory exists
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)

        return True
    except Exception as e:
        logger.error(f"Failed to save JSON to {file_path}: {e}")
        return False


def save_to_csv(data: List[Dict[str, Any]], file_path: str) -> bool:
    """
    Save data to CSV file.

    Args:
        data: List of dictionaries to save
        file_path: Path to save file

    Returns:
        True if successful, False otherwise
    """
    try:
        import pandas as pd
        from pathlib import Path

        if not data:
            logger.warning("No data to save to CSV")
            return False

        # Ensure directory exists
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # Convert to DataFrame and save
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False, encoding='utf-8')

        return True
    except Exception as e:
        logger.error(f"Failed to save CSV to {file_path}: {e}")
        return False
