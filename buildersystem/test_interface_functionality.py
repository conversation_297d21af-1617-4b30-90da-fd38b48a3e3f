#!/usr/bin/env python3
"""
Test the interface functionality programmatically
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append('.')

from structured_query import process_extraction_request, load_available_extractions

def test_interface_functionality():
    """Test the core interface functionality."""
    print("🧪 Testing Interface Functionality")
    print("=" * 50)
    
    # Load extractions
    extractions = load_available_extractions()
    print(f"📁 Available extractions: {list(extractions.keys())}")
    
    if 'spare_parts' not in extractions:
        print("❌ spare_parts extraction not found")
        return
    
    extraction_config = extractions['spare_parts']
    
    # Test the process_extraction_request function
    print("🔄 Testing extraction process...")
    
    try:
        status, json_result, csv_preview = process_extraction_request(
            extraction_type="spare_parts",
            extraction_config=extraction_config,
            file_name="TANK CLEANING MACHINE.pdf",
            use_semantic_search=True,
            max_chunks=3  # Small test
        )
        
        print(f"✅ Status: {status[:100]}...")
        print(f"📊 JSON result length: {len(json_result)}")
        print(f"📋 CSV preview length: {len(csv_preview)}")
        
        # Check if extraction was successful
        if "❌" not in status:
            print("🎉 Extraction successful!")
            
            # Parse JSON to count items
            import json
            try:
                data = json.loads(json_result)
                print(f"📈 Extracted {len(data)} items")
                if data:
                    print(f"🔍 Sample item keys: {list(data[0].keys())}")
            except:
                print("⚠️ Could not parse JSON result")
        else:
            print("❌ Extraction failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_interface_functionality()
