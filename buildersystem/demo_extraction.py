#!/usr/bin/env python3
"""
Demo script showing structured data extraction capabilities.

This script demonstrates:
1. Page-ordered data retrieval
2. Semantic search for relevant content
3. Structured data extraction with different schemas
4. Export to multiple formats (JSON, CSV, Excel)
"""

import json
import os
import sys
from dotenv import load_dotenv

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from structured_query import StructuredDataExtractor

load_dotenv()

def demo_parts_extraction():
    """Demo: Extract parts list with page order preservation."""
    print("🔧 Demo: Parts List Extraction")
    print("=" * 50)
    
    extractor = StructuredDataExtractor()
    
    # Schema for parts extraction
    parts_schema = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "part_number": {"type": "string"},
                "description": {"type": "string"},
                "quantity": {"type": ["string", "number"]},
                "material": {"type": "string"},
                "page_number": {"type": "number"}
            },
            "required": ["part_number", "description"]
        }
    }
    
    # Use semantic search to find parts-related content
    print("🔍 Searching for parts-related content...")
    chunks = extractor.semantic_search(
        "parts list components spare parts", 
        "TANK CLEANING MACHINE.pdf", 
        limit=10
    )
    
    if not chunks:
        print("❌ No parts-related content found")
        return
    
    print(f"📄 Found content on pages: {[c.get('page_number') for c in chunks]}")
    
    # Extract structured data
    print("🤖 Extracting structured parts data...")
    result = extractor.extract_structured_data(
        chunks,
        parts_schema,
        "Extract all parts with their part numbers, descriptions, quantities, and materials. Include the page number where each part was found.",
        preserve_page_order=True
    )
    
    if result.get('success'):
        parts_data = result['data']
        print(f"✅ Extracted {len(parts_data)} parts")
        
        # Show sample results
        print("\n📋 Sample Parts (first 3):")
        for i, part in enumerate(parts_data[:3]):
            print(f"  {i+1}. {part.get('part_number', 'N/A')} - {part.get('description', 'N/A')}")
            print(f"     Qty: {part.get('quantity', 'N/A')}, Material: {part.get('material', 'N/A')}")
            print(f"     Page: {part.get('page_number', 'N/A')}")
        
        # Export to files
        print("\n💾 Exporting to files...")
        timestamp = "demo"
        
        # JSON export
        json_path = f"extracted_data/parts_list_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(parts_data, f, indent=2, ensure_ascii=False)
        print(f"   📄 JSON: {json_path}")
        
        # CSV export
        csv_path = extractor.export_to_csv(parts_data, f"parts_list_{timestamp}")
        if csv_path:
            print(f"   📊 CSV: {csv_path}")
        
        # Excel export
        excel_path = extractor.export_to_excel(parts_data, f"parts_list_{timestamp}")
        if excel_path:
            print(f"   📈 Excel: {excel_path}")
            
    else:
        print(f"❌ Extraction failed: {result.get('error')}")

def demo_table_extraction():
    """Demo: Extract table data maintaining page order."""
    print("\n📊 Demo: Table Data Extraction")
    print("=" * 50)
    
    extractor = StructuredDataExtractor()
    
    # Schema for table extraction
    table_schema = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "table_title": {"type": "string"},
                "headers": {"type": "array", "items": {"type": "string"}},
                "rows": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "additionalProperties": {"type": "string"}
                    }
                },
                "page_number": {"type": "number"}
            }
        }
    }
    
    # Search for table content
    print("🔍 Searching for table content...")
    chunks = extractor.semantic_search(
        "table data specifications breakdown", 
        "TANK CLEANING MACHINE.pdf", 
        limit=8
    )
    
    if not chunks:
        print("❌ No table content found")
        return
    
    print(f"📄 Found content on pages: {[c.get('page_number') for c in chunks]}")
    
    # Extract table data
    print("🤖 Extracting table data...")
    result = extractor.extract_structured_data(
        chunks,
        table_schema,
        "Extract all tables with their titles, headers, and row data. Preserve the structure and include page numbers.",
        preserve_page_order=True
    )
    
    if result.get('success'):
        tables_data = result['data']
        print(f"✅ Extracted {len(tables_data)} tables")
        
        # Show sample results
        print("\n📋 Sample Tables:")
        for i, table in enumerate(tables_data[:2]):
            print(f"  {i+1}. {table.get('table_title', 'Untitled Table')} (Page {table.get('page_number', 'N/A')})")
            headers = table.get('headers', [])
            if headers:
                print(f"     Headers: {', '.join(headers[:3])}{'...' if len(headers) > 3 else ''}")
            rows = table.get('rows', [])
            print(f"     Rows: {len(rows)}")
            
    else:
        print(f"❌ Table extraction failed: {result.get('error')}")

def demo_sequential_processing():
    """Demo: Process pages sequentially maintaining order."""
    print("\n📖 Demo: Sequential Page Processing")
    print("=" * 50)
    
    extractor = StructuredDataExtractor()
    
    # Get first 5 pages in order
    print("📄 Getting first 5 pages in sequential order...")
    pages = extractor.get_file_pages_ordered("TANK CLEANING MACHINE.pdf")
    first_pages = pages[:5]
    
    # Schema for page summary
    summary_schema = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "page_number": {"type": "number"},
                "content_type": {"type": "string"},
                "main_topic": {"type": "string"},
                "has_images": {"type": "boolean"},
                "has_tables": {"type": "boolean"},
                "key_information": {"type": "array", "items": {"type": "string"}}
            }
        }
    }
    
    # Extract page summaries
    print("🤖 Analyzing page content...")
    result = extractor.extract_structured_data(
        first_pages,
        summary_schema,
        "Analyze each page and provide a summary including content type, main topic, presence of images/tables, and key information points.",
        preserve_page_order=True
    )
    
    if result.get('success'):
        summaries = result['data']
        print(f"✅ Analyzed {len(summaries)} pages")
        
        # Show results
        print("\n📋 Page Analysis:")
        for summary in summaries:
            page_num = summary.get('page_number', 'N/A')
            topic = summary.get('main_topic', 'N/A')
            content_type = summary.get('content_type', 'N/A')
            print(f"  Page {page_num}: {topic} ({content_type})")
            
            features = []
            if summary.get('has_images'):
                features.append("Images")
            if summary.get('has_tables'):
                features.append("Tables")
            if features:
                print(f"    Features: {', '.join(features)}")
                
    else:
        print(f"❌ Analysis failed: {result.get('error')}")

def main():
    """Run all demos."""
    print("🚀 Structured Data Extraction Demo")
    print("=" * 60)
    
    try:
        # Check if we have data
        extractor = StructuredDataExtractor()
        files = extractor.get_available_files()
        
        if not files:
            print("❌ No PDF files found in database")
            print("   Please run: python ingest_pdf.py 'path/to/your/pdf'")
            return
        
        print(f"📁 Using file: {files[0]}")
        
        # Run demos
        demo_parts_extraction()
        demo_table_extraction()
        demo_sequential_processing()
        
        print("\n🎉 Demo completed!")
        print("📁 Check the 'extracted_data' folder for exported files")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
