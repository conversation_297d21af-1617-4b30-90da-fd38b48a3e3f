#!/usr/bin/env python3
"""
Test script for PDF ingestion functionality.

This script tests various components of the PDF ingestion pipeline:
1. Environment setup validation
2. Database connection
3. PDF processing components
4. Integration test with sample PDF
"""

import json
import os
import sys
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add the current directory to Python path to import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ingest_pdf import PDFProcessor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TestPDFProcessor(unittest.TestCase):
    """Test cases for PDFProcessor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = PDFProcessor()
    
    def test_environment_variables(self):
        """Test that required environment variables are set."""
        required_vars = ["OPENAI_API_KEY", "SUPABASE_URL", "SUPABASE_KEY"]
        
        for var in required_vars:
            value = os.getenv(var)
            self.assertIsNotNone(value, f"Environment variable {var} is not set")
            self.assertNotEqual(value.strip(), "", f"Environment variable {var} is empty")
    
    def test_processor_initialization(self):
        """Test that PDFProcessor initializes correctly."""
        self.assertIsNotNone(self.processor.openai_client)
        self.assertIsNotNone(self.processor.supabase)
        self.assertEqual(self.processor.embedding_model, "text-embedding-3-small")
        self.assertIn("total_pages", self.processor.stats)
        self.assertIn("pages_with_tables", self.processor.stats)
        self.assertIn("total_images", self.processor.stats)
    
    def test_markdown_content_generation(self):
        """Test markdown content generation."""
        page_num = 0
        page_text = "This is sample page text with some content."
        table_data = {
            "is_continuation": False,
            "table_title": "Sample Table",
            "rows": [
                {"Name": "John", "Age": "30"},
                {"Name": "Jane", "Age": "25"}
            ]
        }
        image_paths = ["./images/page1_img1.png", "./images/page1_img2.png"]
        
        markdown = self.processor.create_markdown_content(
            page_num, page_text, table_data, image_paths
        )
        
        # Check that all expected sections are present
        self.assertIn("📄 Page 1", markdown)
        self.assertIn("🧾 Table: Sample Table", markdown)
        self.assertIn("🖼️ Images", markdown)
        self.assertIn("📚 Full Page Text", markdown)
        self.assertIn("| Name | Age |", markdown)
        self.assertIn("| John | 30 |", markdown)
        self.assertIn("./images/page1_img1.png", markdown)
    
    def test_markdown_content_without_table(self):
        """Test markdown content generation without tables."""
        page_num = 1
        page_text = "This is sample page text without tables."
        table_data = None
        image_paths = []
        
        markdown = self.processor.create_markdown_content(
            page_num, page_text, table_data, image_paths
        )
        
        # Check basic structure
        self.assertIn("📄 Page 2", markdown)
        self.assertIn("📚 Full Page Text", markdown)
        self.assertIn(page_text, markdown)
        # Should not contain table or image sections
        self.assertNotIn("🧾 Table:", markdown)
        self.assertNotIn("🖼️ Images", markdown)
    
    @patch('ingest_pdf.OpenAI')
    def test_table_structuring_with_mock(self, mock_openai_class):
        """Test table structuring with mocked OpenAI response."""
        # Mock the OpenAI client and response
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "is_continuation": False,
            "table_title": "Test Table",
            "rows": [{"Col1": "Val1", "Col2": "Val2"}]
        })
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create a new processor instance to use the mocked client
        processor = PDFProcessor()
        
        page_text = "Sample table data here"
        result = processor.detect_and_structure_tables(page_text, True)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["table_title"], "Test Table")
        self.assertFalse(result["is_continuation"])
        self.assertEqual(len(result["rows"]), 1)
    
    def test_table_structuring_no_tables(self):
        """Test table structuring when no tables are present."""
        page_text = "This is just regular text without any tables."
        result = self.processor.detect_and_structure_tables(page_text, False)
        
        self.assertIsNone(result)
    
    @patch('ingest_pdf.fitz')
    def test_image_extraction_mock(self, mock_fitz):
        """Test image extraction with mocked PyMuPDF."""
        # Mock PyMuPDF components
        mock_doc = Mock()
        mock_page = Mock()
        mock_page.get_images.return_value = [(1, 0, 0, 0, 0, 0, 0, 0)]  # Mock image reference

        # Configure the mock document to return the mock page when indexed
        mock_doc.__getitem__ = Mock(return_value=mock_page)
        mock_fitz.open.return_value = mock_doc

        # Mock Pixmap
        mock_pixmap = Mock()
        mock_pixmap.n = 3  # RGB
        mock_pixmap.alpha = 0
        mock_pixmap.save = Mock()  # Mock the save method
        mock_fitz.Pixmap.return_value = mock_pixmap

        # Create images directory for test
        images_dir = Path("./images")
        images_dir.mkdir(exist_ok=True)

        # Test image extraction
        result = self.processor.extract_images_from_page("dummy_path.pdf", 0)

        # Should return list of image paths
        self.assertIsInstance(result, list)
    
    def test_database_setup(self):
        """Test database setup (this will actually try to connect to Supabase)."""
        try:
            self.processor.setup_database()
            print("✅ Database setup test passed")
        except Exception as e:
            self.fail(f"Database setup failed: {e}")

class TestIntegration(unittest.TestCase):
    """Integration tests with actual PDF file."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.processor = PDFProcessor()
        self.sample_pdf_path = Path("sample/TANK CLEANING MACHINE.pdf")
    
    def test_sample_pdf_exists(self):
        """Test that the sample PDF file exists."""
        self.assertTrue(self.sample_pdf_path.exists(), 
                       f"Sample PDF not found at {self.sample_pdf_path}")
    
    @unittest.skipIf(not Path("sample/TANK CLEANING MACHINE.pdf").exists(), 
                     "Sample PDF not available")
    def test_pdf_processing_integration(self):
        """Integration test with actual PDF processing."""
        print(f"\n🧪 Running integration test with {self.sample_pdf_path}")
        
        # This will actually process the PDF and insert into database
        # Only run if you want to test the full pipeline
        success = self.processor.process_pdf(str(self.sample_pdf_path))
        
        self.assertTrue(success, "PDF processing should succeed")
        self.assertGreater(self.processor.stats["total_pages"], 0, 
                          "Should process at least one page")
        
        # Print stats
        self.processor.print_summary()

def run_unit_tests():
    """Run unit tests only."""
    print("🧪 Running Unit Tests")
    print("=" * 50)
    
    # Create test suite with only unit tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add unit tests
    suite.addTests(loader.loadTestsFromTestCase(TestPDFProcessor))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_integration_tests():
    """Run integration tests."""
    print("\n🧪 Running Integration Tests")
    print("=" * 50)
    
    # Create test suite with integration tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add integration tests
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def main():
    """Main test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test PDF ingestion functionality")
    parser.add_argument("--integration", action="store_true", 
                       help="Run integration tests (will process actual PDF)")
    parser.add_argument("--unit", action="store_true", 
                       help="Run unit tests only")
    
    args = parser.parse_args()
    
    if not args.integration and not args.unit:
        # Default: run unit tests only
        args.unit = True
    
    success = True
    
    if args.unit:
        success &= run_unit_tests()
    
    if args.integration:
        success &= run_integration_tests()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
