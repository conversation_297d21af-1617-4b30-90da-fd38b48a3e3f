Marine Equipment Data Extraction - Instructions:

** Objective: **
- Extract **ALL spare parts** details from the given marine document with structured accuracy for procurement of marine machinery inventory

** Data Fields to Extract: **
1. ** Equipment Name: **
- Identify the equipment name of the spare parts
2. ** Part Name: **
- Extract the **Name of the Part** for Procurement.
3. ** Part Number: **
- Extract the **Part Number** associated with spare parts.
4. ** Drawing Number: **
- Extract the **Drawing Number** associated with the spare parts.
- Refer to **assembly drawings** for mapping the **Drawing Number**
5. ** Position Number: **
- Extract the **Position Number** associated with the spare parts.
- Refer to **assembly drawings** for mapping the **position numbers**
6. ** Quantity (Qty): **
- Extract the **working Qty** associated with the spare parts.
7. ** Units: **
- Extract the **Units for working Qty** associated with the spare parts.
8. ** Materials: **
- Extract the **Materials** associated with the spare parts.
9. ** Remarks: **
- Extract the **Remarks** associated with the spare parts.
- **Do not map Remarks with any other field**
10. ** Spare Part Title: **
- Extract the **Title** of the spare table associated with the spare parts.
- Ensure **Exact title** as per the table is extracted.
11. ** PDF Reference: **
- Identify accurate **PDF page numbers** where the Spare part details extracted.
- create a **fresh sequential page number mapping starting from '1'** for **each page** of the document and **update the PDF Reference** accordingly
12.**Output Format (CSV Columns Order):**
	**Equipment Name**
	**Part Name**
	**Part Number**
	**Drawing Number**
	**Position Number**
	**Quantity (Qty)**
	**Units**
	**Materials**
	**Remarks**
	**Spare Part Title**
	**PDF Reference**
- strictly Provide **output in CSV format** for **copy paste to excel**
13.**Additional Requirements:**
- Extract spare parts from **general arrangement drawings, sections views and sub assembly drawings**
- Pay special attention to **ALL drawings** to extract spare parts
- Include **Yard supplied spare parts" if available
- Include **ALL parts with part numbers**, even when complete information is not available
- For missing information, use **"N/A"** rather than leaving fields blank
- Pay special attention to components mentioned in **technical descriptions** but not explicitly listed in spare parts sections
- Ensure **ALL hydraulic components, electrical equipment, and technical documentation** are included
- Ensure **logical and sequential arrangement** of data.
- Cross-reference **assembly group numbers** with part descriptions to ensure complete coverage
- Verify that **EVERY equipment category** listed in the **Table of Contents** has representation in the final output
- Before finalizing, **validate page numbers** to ensure they **do not exceed the document length.**
