{"type": "object", "properties": {"components": {"type": "array", "items": {"type": "object", "properties": {"equipment_name": {"type": "string", "description": "Equipment name following parent-child hierarchy (e.g., Motor - Freshwater Pump - Freshwater Generator)"}, "maker_name": {"type": "string", "description": "Manufacturer name (mark as N/A if unavailable)"}, "model": {"type": "string", "description": "Model/type number (separate multiple models with |, mark as N/A if missing)"}, "serial_number": {"type": "string", "description": "Serial or manufacturer number (mark as N/A if missing)"}, "particulars": {"type": "string", "description": "Technical specs like power, capacity, flow, pressure, voltage, dimensions"}, "motor_capacity": {"type": "string", "description": "Motor rating in kW/HP (if applicable, else N/A)"}, "quantity": {"type": "string", "description": "Number of identical units onboard"}, "component_type": {"type": "string", "description": "Component category (e.g., Centrifugal Pump, Gearbox, Ejector, Panel, Electric Motor)"}, "pdf_reference": {"type": "string", "description": "Sequential page number(s) starting from 1 (separate multiple pages with |)"}, "spare_pages": {"type": "string", "description": "Pages listing spare parts (only map once per unique equipment, separate with |)"}, "job_pages": {"type": "string", "description": "Pages listing maintenance/inspection/service procedures (separate with |)"}}, "required": ["equipment_name", "maker_name", "model", "serial_number", "particulars", "motor_capacity", "quantity", "component_type", "pdf_reference", "spare_pages", "job_pages"]}}}, "required": ["components"]}