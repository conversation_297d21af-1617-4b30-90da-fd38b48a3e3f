#!/usr/bin/env python3
"""
Test All Extraction Types
"""

import json
from structured_query import load_available_extractions

def test_all_extractions():
    """Test all available extraction types."""
    
    print("🧪 Testing All Extraction Types...")
    print("=" * 50)
    
    # Load available extractions
    extractions = load_available_extractions()
    
    print(f"📋 Total extractions available: {len(extractions)}")
    print(f"🎯 Extraction types: {list(extractions.keys())}")
    print()
    
    # Test each extraction type
    for extraction_name, config in extractions.items():
        print(f"🔍 Testing: {extraction_name}")
        print(f"   📝 Display Name: {config['name']}")
        print(f"   🔍 Search Queries: {len(config['search_queries'])} queries")
        
        # Validate schema
        schema = config['schema']
        if 'properties' in schema:
            main_property = list(schema['properties'].keys())[0]
            print(f"   📋 Main Schema Property: {main_property}")
            
            if 'items' in schema['properties'][main_property]:
                item_properties = schema['properties'][main_property]['items']['properties']
                print(f"   📊 Fields: {list(item_properties.keys())}")
            
        # Show first few search queries
        if config['search_queries']:
            print(f"   🔍 Sample queries: {', '.join(config['search_queries'][:3])}...")
        
        print(f"   ✅ {extraction_name} - Setup Complete")
        print()
    
    print("=" * 50)
    print("🎉 All extraction types are properly configured!")
    
    # Summary
    print("\n📊 SUMMARY:")
    print("- ✅ Spare Parts Extraction")
    print("- ✅ Component Extraction") 
    print("- ✅ Job Extraction (PMS)")
    print("\n🚀 Ready to use in Gradio interface!")

if __name__ == "__main__":
    test_all_extractions()
