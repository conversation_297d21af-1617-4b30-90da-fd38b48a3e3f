import os
import pandas as pd
from xgboost import XGBClassifier
from sklearn.preprocessing import LabelEncoder
import joblib


def train_model_for_client():
    excel_path = os.path.join("cow_data.xlsx")

    try:
        obs_df = pd.read_excel(excel_path, sheet_name="sheet1")
        rx_df = pd.read_excel(excel_path, sheet_name="sheet2")

        obs_df = obs_df[~obs_df['care_calendar_id'].astype(str).str.contains("care_calendar_id", na=False)]

        # Pivot and preprocess
        pivot = obs_df.pivot_table(index='care_calendar_id', columns='Observation', values='Response', aggfunc='first').reset_index()
        diagnoses = obs_df[['care_calendar_id', 'Diagnosis']].drop_duplicates()
        merged_obs = pd.merge(pivot, diagnoses, on='care_calendar_id', how='left')

        # Merge with Rx
        full_data = pd.merge(merged_obs, rx_df, on='Diagnosis', how='inner')

        # Rename columns
        full_data = full_data.rename(columns={
            'Diagnosis': 'diagnosis',
            'Breed': 'breed',
            'No. of Calvings': 'num_calvings',
            'Age as of 24-Feb-2025': 'age',
            'Months Pregnant as of 24-Feb-2025': 'months_pregnant',
            'Months Since Calving As of 24-Feb-2025': 'months_since_calving',
            'Average Animal LPD': 'avg_lpd'
        })

        # Drop rows with missing important fields
        required_cols = ['diagnosis', 'breed', 'num_calvings', 'age', 'months_pregnant', 'months_since_calving', 'avg_lpd', 'Medicine']
        full_data = full_data.dropna(subset=required_cols)

        # Label encode target
        le = LabelEncoder()
        full_data['Medicine_enc'] = le.fit_transform(full_data['Medicine'])

        # One-hot encode features
        X = pd.get_dummies(full_data[['diagnosis', 'breed', 'num_calvings', 'age', 'months_pregnant', 'months_since_calving', 'avg_lpd']])
        y = full_data['Medicine_enc']

        # Train
        model = XGBClassifier(objective='multi:softprob', eval_metric='mlogloss', use_label_encoder=False)
        model.fit(X, y)

        print(f"✅ Successfully trained XGBoost model")
        print(f"   - Features: {X.shape[1]}")
        print(f"   - Samples: {X.shape[0]}")
        print(f"   - Classes: {len(le.classes_)}")

    except Exception as e:
        print(f"❌ Failed : {e}")


if __name__ == "__main__":
    train_model_for_client()