import os
import pandas as pd
from xgboost import XGBClassifier
from sklearn.preprocessing import LabelEncoder
import joblib
import json


def train_model_for_client():
    excel_path = os.path.join("cow_data.xlsx")

    try:
        obs_df = pd.read_excel(excel_path, sheet_name="Observation to Diagnosis")
        rx_df = pd.read_excel(excel_path, sheet_name="Diagnosis to Rx")

        obs_df = obs_df[~obs_df['care_calendar_id'].astype(str).str.contains("care_calendar_id", na=False)]
        pivot = obs_df.pivot_table(index='care_calendar_id', columns='Observation', values='Response', aggfunc='first').reset_index()
        diagnoses = obs_df[['care_calendar_id', 'Diagnosis']].drop_duplicates()

        merged_obs = pd.merge(pivot, diagnoses, on='care_calendar_id', how='left')
        full_data = pd.merge(merged_obs, rx_df, on='Diagnosis', how='inner')

        
        sample_row = full_data.iloc[0].to_dict()
        print(json.dumps(sample_row, indent=2, default=str))

        # Rename columns
        full_data = full_data.rename(columns={
            'Diagnosis': 'diagnosis',
            'Breed': 'breed',
            'No. of Calvings': 'num_calvings',
            'Age as of 24-Feb-2025': 'age',
            'Months Pregnant as of 24-Feb-2025': 'months_pregnant',
            'Months Since Calving As of 24-Feb-2025': 'months_since_calving',
            'Average Animal LPD': 'avg_lpd'
        })

        # Drop rows with missing important fields
        required_cols = ['diagnosis', 'breed', 'num_calvings', 'age', 'months_pregnant', 'months_since_calving', 'avg_lpd', 'Medicine']
        full_data = full_data.dropna(subset=required_cols)

        sample_row = full_data.iloc[0].to_dict()
        print(json.dumps(sample_row, indent=2, default=str))

        # Label encode target
        le = LabelEncoder()
        print(le.fit_transform(full_data['Medicine']))
        full_data['Medicine_enc'] = le.fit_transform(full_data['Medicine'])

        sample_row = full_data.iloc[0].to_dict()
        print(json.dumps(sample_row, indent=2, default=str))

        # Print one element of full_data as JSON
        print("\n" + "="*60)
        print("SAMPLE FULL_DATA ELEMENT (as JSON):")
        print("="*60)
        sample_row = full_data.iloc[0].to_dict()
        print(json.dumps(sample_row, indent=2, default=str))

        # One-hot encode features
        X = pd.get_dummies(full_data[['diagnosis', 'breed', 'num_calvings', 'age', 'months_pregnant', 'months_since_calving', 'avg_lpd']])
        y = full_data['Medicine_enc']

        # Train
        model = XGBClassifier(objective='multi:softprob', eval_metric='mlogloss')
        model.fit(X, y)

        print(f"✅ Successfully trained XGBoost model")
        print(f"   - Features: {X.shape[1]}")
        print(f"   - Samples: {X.shape[0]}")
        print(f"   - Classes: {len(le.classes_)}")

        # Print sample data
        print("\n" + "="*60)
        print("TRAINING DATA SAMPLES:")
        print("="*60)

        # Show first 10 rows of features
        print("\nFeature Matrix (X) - First 10 samples:")
        print("-" * 40)
        print(X[:10])

        # Show first 10 labels
        print(f"\nTarget Labels (y) - First 10 samples:")
        print("-" * 40)
        print(y[:10])

        # Show label encoder classes
        print(f"\nLabel Encoder Classes:")
        print("-" * 40)
        for i, class_name in enumerate(le.classes_):
            print(f"  {i}: {class_name}")

        # Show feature importance if available
        if hasattr(model, 'feature_importances_'):
            print(f"\nTop 10 Feature Importances:")
            print("-" * 40)
            feature_importance = model.feature_importances_
            # Get top 10 features
            top_indices = feature_importance.argsort()[-10:][::-1]
            for i, idx in enumerate(top_indices):
                print(f"  Feature {idx}: {feature_importance[idx]:.4f}")

        # Show some predictions on training data
        print(f"\nSample Predictions vs Actual (First 10):")
        print("-" * 40)
        predictions = model.predict(X[:10])
        predicted_labels = le.inverse_transform(predictions)
        actual_labels = le.inverse_transform(y[:10])

        for i in range(10):
            print(f"  Sample {i+1}: Predicted='{predicted_labels[i]}', Actual='{actual_labels[i]}'")

    except Exception as e:
        print(f"❌ Failed : {e}")


if __name__ == "__main__":
    train_model_for_client()