from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.chunking import HybridChunker

converter = DocumentConverter()

# Convert with specific options
result = converter.convert(
    "mac.pdf"
)

# Access different content types
document = result.document

chunker = HybridChunker()
chunk_iter = chunker.chunk(dl_doc=document)

for i, chunk in enumerate(chunk_iter):
    print(f"=== {i} ===")
    print(f"chunk.text :\n{chunk.text!r}")

    # enriched_text = chunker.contextualize(chunk=chunk)
    # print(f"chunker.contextualize(chunk):\n{f'{enriched_text[:300]}…'!r}")

    print()
