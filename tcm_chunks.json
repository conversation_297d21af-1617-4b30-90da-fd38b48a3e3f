[{"chunk_id": "chunk_0000", "title": "<PERSON><PERSON><PERSON>", "content": "FinaRl durbarwiki ngs", "page_range": "1", "section_hierarchy": [], "chunk_type": "text", "metadata": {"page_number": 1, "section_path": "", "content_lines": 1}, "token_count": 9, "semantic_summary": "FinaRl durbarwiki ngs"}, {"chunk_id": "chunk_0001", "title": "Including: Jinling Shipyard", "content": "Including: Jinling Shipyard\nJLZ 070405\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530", "page_range": "1-1", "section_hierarchy": ["Including: Jinling Shipyard"], "chunk_type": "section", "metadata": {"page_number": 1, "section_path": "Including: Jinling Shipyard", "content_lines": 4, "merged": true}, "token_count": 38, "semantic_summary": "Including: Jinling Shipyard\nJLZ 070405\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530."}, {"chunk_id": "chunk_0003", "title": "Web: www.scanjet.se Scanjet no.81593", "content": "Web: www.scanjet.se Scanjet no.81593", "page_range": "1", "section_hierarchy": ["Including: Jinling Shipyard", "Web: www.scanjet.se Scanjet no.81593"], "chunk_type": "section", "metadata": {"page_number": 1, "section_path": "Including: Jinling Shipyard > Web: www.scanjet.se Scanjet no.81593", "content_lines": 1}, "token_count": 12, "semantic_summary": "Web: www.scanjet.se Scanjet no.81593"}, {"chunk_id": "chunk_0004", "title": "Tel: + 46 31 338 7530: <PERSON><PERSON><PERSON>", "content": "FinaRl durbarwiki ngs", "page_range": "2", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 2, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 1}, "token_count": 9, "semantic_summary": "FinaRl durbarwiki ngs"}, {"chunk_id": "chunk_0005", "title": "Including: Jinling Shipyard", "content": "Including: Jinling Shipyard\nJLZ 070406\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530", "page_range": "2-2", "section_hierarchy": ["Including: Jinling Shipyard", "Including: Jinling Shipyard"], "chunk_type": "section", "metadata": {"page_number": 2, "section_path": "Including: Jinling Shipyard > Including: Jinling Shipyard", "content_lines": 4, "merged": true}, "token_count": 38, "semantic_summary": "Including: Jinling Shipyard\nJLZ 070406\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530."}, {"chunk_id": "chunk_0007", "title": "Web: www.scanjet.se Scanjet no.81594", "content": "Web: www.scanjet.se Scanjet no.81594", "page_range": "2", "section_hierarchy": ["Including: Jinling Shipyard", "Web: www.scanjet.se Scanjet no.81594"], "chunk_type": "section", "metadata": {"page_number": 2, "section_path": "Including: Jinling Shipyard > Web: www.scanjet.se Scanjet no.81594", "content_lines": 1}, "token_count": 12, "semantic_summary": "Web: www.scanjet.se Scanjet no.81594"}, {"chunk_id": "chunk_0008", "title": "Tel: + 46 31 338 7530: <PERSON><PERSON><PERSON>", "content": "FinaRl durbarwiki ngs", "page_range": "3", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 3, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 1}, "token_count": 9, "semantic_summary": "FinaRl durbarwiki ngs"}, {"chunk_id": "chunk_0009", "title": "Including: Jinling Shipyard", "content": "Including: Jinling Shipyard\nJLZ 070407\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530", "page_range": "3-3", "section_hierarchy": ["Including: Jinling Shipyard", "Including: Jinling Shipyard"], "chunk_type": "section", "metadata": {"page_number": 3, "section_path": "Including: Jinling Shipyard > Including: Jinling Shipyard", "content_lines": 4, "merged": true}, "token_count": 38, "semantic_summary": "Including: Jinling Shipyard\nJLZ 070407\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530."}, {"chunk_id": "chunk_0011", "title": "Web: www.scanjet.se Scanjet no.81595", "content": "Web: www.scanjet.se Scanjet no.81595", "page_range": "3", "section_hierarchy": ["Including: Jinling Shipyard", "Web: www.scanjet.se Scanjet no.81595"], "chunk_type": "section", "metadata": {"page_number": 3, "section_path": "Including: Jinling Shipyard > Web: www.scanjet.se Scanjet no.81595", "content_lines": 1}, "token_count": 12, "semantic_summary": "Web: www.scanjet.se Scanjet no.81595"}, {"chunk_id": "chunk_0012", "title": "Tel: + 46 31 338 7530: <PERSON><PERSON><PERSON>", "content": "FinaRl durbarwiki ngs", "page_range": "4", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 4, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 1}, "token_count": 9, "semantic_summary": "FinaRl durbarwiki ngs"}, {"chunk_id": "chunk_0013", "title": "Including: Jinling Shipyard", "content": "Including: Jinling Shipyard\nJLZ 070408\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530", "page_range": "4-4", "section_hierarchy": ["Including: Jinling Shipyard", "Including: Jinling Shipyard"], "chunk_type": "section", "metadata": {"page_number": 4, "section_path": "Including: Jinling Shipyard > Including: Jinling Shipyard", "content_lines": 4, "merged": true}, "token_count": 38, "semantic_summary": "Including: Jinling Shipyard\nJLZ 070408\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530."}, {"chunk_id": "chunk_0015", "title": "Web: www.scanjet.se Scanjet no.81596", "content": "Web: www.scanjet.se Scanjet no.81596", "page_range": "4", "section_hierarchy": ["Including: Jinling Shipyard", "Web: www.scanjet.se Scanjet no.81596"], "chunk_type": "section", "metadata": {"page_number": 4, "section_path": "Including: Jinling Shipyard > Web: www.scanjet.se Scanjet no.81596", "content_lines": 1}, "token_count": 12, "semantic_summary": "Web: www.scanjet.se Scanjet no.81596"}, {"chunk_id": "chunk_0016", "title": "Tel: + 46 31 338 7530: <PERSON><PERSON><PERSON>", "content": "FinaRl durbarwiki ngs", "page_range": "5", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 5, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 1}, "token_count": 9, "semantic_summary": "FinaRl durbarwiki ngs"}, {"chunk_id": "chunk_0017", "title": "Including: Jinling Shipyard", "content": "Including: Jinling Shipyard\nJLZ 070409\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530", "page_range": "5-5", "section_hierarchy": ["Including: Jinling Shipyard", "Including: Jinling Shipyard"], "chunk_type": "section", "metadata": {"page_number": 5, "section_path": "Including: Jinling Shipyard > Including: Jinling Shipyard", "content_lines": 4, "merged": true}, "token_count": 38, "semantic_summary": "Including: Jinling Shipyard\nJLZ 070409\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530."}, {"chunk_id": "chunk_0019", "title": "Web: www.scanjet.se Scanjet no.81597", "content": "Web: www.scanjet.se Scanjet no.81597", "page_range": "5", "section_hierarchy": ["Including: Jinling Shipyard", "Web: www.scanjet.se Scanjet no.81597"], "chunk_type": "section", "metadata": {"page_number": 5, "section_path": "Including: Jinling Shipyard > Web: www.scanjet.se Scanjet no.81597", "content_lines": 1}, "token_count": 12, "semantic_summary": "Web: www.scanjet.se Scanjet no.81597"}, {"chunk_id": "chunk_0020", "title": "Tel: + 46 31 338 7530: <PERSON><PERSON><PERSON>", "content": "FinaRl durbarwiki ngs", "page_range": "6", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 6, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 1}, "token_count": 9, "semantic_summary": "FinaRl durbarwiki ngs"}, {"chunk_id": "chunk_0021", "title": "Including: Jinling Shipyard", "content": "Including: Jinling Shipyard\nJLZ 070410\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530", "page_range": "6-6", "section_hierarchy": ["Including: Jinling Shipyard", "Including: Jinling Shipyard"], "chunk_type": "section", "metadata": {"page_number": 6, "section_path": "Including: Jinling Shipyard > Including: Jinling Shipyard", "content_lines": 4, "merged": true}, "token_count": 38, "semantic_summary": "Including: Jinling Shipyard\nJLZ 070410\n57K DWT Bulk carrier\nScanjet Marine AB\n\nTel: + 46 31 338 7530."}, {"chunk_id": "chunk_0023", "title": "Web: www.scanjet.se Scanjet no.81598", "content": "Web: www.scanjet.se Scanjet no.81598", "page_range": "6", "section_hierarchy": ["Including: Jinling Shipyard", "Web: www.scanjet.se Scanjet no.81598"], "chunk_type": "section", "metadata": {"page_number": 6, "section_path": "Including: Jinling Shipyard > Web: www.scanjet.se Scanjet no.81598", "content_lines": 1}, "token_count": 12, "semantic_summary": "Web: www.scanjet.se Scanjet no.81598"}, {"chunk_id": "chunk_0024", "title": "Tel: + 46 31 338 7530: Instruction Manual", "content": "Instruction Manual\nSC90T2\nCrude", "page_range": "7", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 7, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 3}, "token_count": 10, "semantic_summary": "Instruction Manual\nSC90T2\nCrude"}, {"chunk_id": "chunk_0025", "title": "Revision: SC90T2-1-07-080710", "content": "Revision: SC90T2-1-07-080710\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "7", "section_hierarchy": ["Including: Jinling Shipyard", "Revision: SC90T2-1-07-080710"], "chunk_type": "section", "metadata": {"page_number": 7, "section_path": "Including: Jinling Shipyard > Revision: SC90T2-1-07-080710", "content_lines": 9}, "token_count": 84, "semantic_summary": "Revision: SC90T2-1-07-080710\nScanjet Marine AB\nSödra Långebergsgatan 36\nP. O."}, {"chunk_id": "chunk_0026", "title": "Table from Including: Jinling Shipyard > Revision: SC90T2-1-07-080710", "content": "Table ID: table_7_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_7_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "7-7", "section_hierarchy": ["Including: Jinling Shipyard", "Revision: SC90T2-1-07-080710"], "chunk_type": "table", "metadata": {"page_number": 7, "table_id": "table_7_0", "table_rows": 13, "table_cols": 1, "merged": true}, "token_count": 90, "semantic_summary": "Table ID: table_7_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_7_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0028", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 1 (35)\n# SC 90T2-CRUDE-07", "page_range": "8", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 8, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 1 (35)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0029", "title": "This manual apply for the following products:", "content": "This manual apply for the following products:\nType Date\nSC 90T2-CRUDE 2008-07-10\n\nFile: SC90T2-1-07-080710.doc", "page_range": "8-8", "section_hierarchy": ["Including: Jinling Shipyard", "This manual apply for the following products:"], "chunk_type": "section", "metadata": {"page_number": 8, "section_path": "Including: Jinling Shipyard > This manual apply for the following products:", "content_lines": 3, "merged": true}, "token_count": 41, "semantic_summary": "This manual apply for the following products:\nType Date\nSC 90T2-CRUDE 2008-07-10\n\nFile: SC90T2-1-07-080710. doc."}, {"chunk_id": "chunk_0031", "title": "Contact information:", "content": "Contact information:\n\nSales office: Spareparts department / Production unit:\nScanjet Marine AB Scanjet Marine AB\nSödra Långebergsgatan 36 Törnedalsgatan 1\nP.O. Box 9316 P.O. Box 2\nSE-400 97 Göteborg, Sweden SE-275 21 Sjöbo, Sweden\nTelephone +46 31 338 7530 Telephone +46 416 513 100\nTelefax +46 31 338 7540 Telefax +46 416 511 656\nEmail <EMAIL> <EMAIL>\nWeb www.scanjet.se\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "8-8", "section_hierarchy": ["Including: Jinling Shipyard", "Contact information:"], "chunk_type": "section", "metadata": {"page_number": 8, "section_path": "Including: Jinling Shipyard > Contact information:", "content_lines": 1, "merged": true}, "token_count": 208, "semantic_summary": "Sales office: Spareparts department / Production unit: Scanjet Marine AB. Phone: +46 31 338 7530. Email: <EMAIL>."}, {"chunk_id": "chunk_0033", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 2 (36)\n# SC 90T2-CRUDE-07", "page_range": "9", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 9, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 2 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0034", "title": "CONTENTS", "content": "CONTENTS\n\nDESC<PERSON><PERSON><PERSON>ON PAGE\nCONTACT INFORMATION 1\nINTRODUCTION 3\nWORKING PRINCIPE 4\nTECHNICAL DATA 5-7", "page_range": "9-9", "section_hierarchy": ["CONTENTS"], "chunk_type": "section", "metadata": {"page_number": 9, "section_path": "CONTENTS", "content_lines": 1, "merged": true}, "token_count": 34, "semantic_summary": "CONTENTS\n\nDESC<PERSON><PERSON><PERSON><PERSON> PAGE\nCONTACT INFORMATION 1\nINTRODUCTION 3\nWORKING PRINCIPE 4\nTECHNICAL DATA 5-7."}, {"chunk_id": "chunk_0036", "title": "OPERATION", "content": "OPERATION\n\nMAINTENANCE\nREMOVAL OF DRIVING UNIT 23\nREMOVAL OF INLET HOUSE 24\nFAULT FINDING 25\nTOOL KIT 26\nSPARE PART KIT /VESSEL 27\nSERVICE KITS 28\nHOW TO ORDER SPARE PARTS 29\nSPARE PART LIST 30-35\nNOTES 36\nThis manual is intended to assist in the handling and operation of the Scanjet SC90T2\nTank Cleaning System. Continuous product improvement is the policy of Scanjet\nMarine AB and we reserve the right to alter the specifications at any time without prior\nnotice.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "9-9", "section_hierarchy": ["OPERATION"], "chunk_type": "section", "metadata": {"page_number": 9, "section_path": "OPERATION", "content_lines": 1, "merged": true}, "token_count": 206, "semantic_summary": "This manual is intended to assist in the handling and operation of the Scanjet SC90T2Tank Cleaning System. Continuous product improvement is the policy of Scanjet Marine AB and we reserve the right to alter the specifications at any time."}, {"chunk_id": "chunk_0038", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 3 (36)\n# SC 90T2-CRUDE-07", "page_range": "10", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 10, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 3 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0039", "title": "INTRODUCTION", "content": "INTRODUCTION\n\nINSTALLATION GUIDE\nSCANJET model SC90T2 is a tank-cleaning machine specially\ndeveloped for cleaning of cargo and slop tanks onboard crude\noil carriers. The size, construction and cleaning requirements of\nthese tanks are design criteria, which have been evaluated prior\nto installation in your vessel.\nSCANJET SC90T2 tank cleaning machine consists of two main\nparts. One washing unit that is fixed installed in deck and a\nturbine driven drive unit.\nThe washing unit is tailor-made for each specific tank in respect\nto size of nozzle and length of main pipe.\nThe cleaning procedure will start by opening the valve for\ncleaning media. The drive unit will now move the nozzle and\nturn the main pipe creating a horizontal spiral-cleaning pattern.\nWhen the cleaning procedure is finalised the valve is to be\nclosed.\nIMPORTANT!\nNOTE! When handling the tankcleaning machine never lift the machine by\nthe nozzletube.\nNOTE! The use of tankcleaning machine may cause static electricity why\nmeasures must be taken in order to avoid sparks and possible explosion by\nhaving the tanks inert at time of cleaning.\nNOTE! Flush the supply line carefully before connecting the tankcleaning\nmachine for main supply. Scanjet will not take any responsibility for rough dirt\nand particles in the supply line, causing malfunction to the machine.\nNozzle tube\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "10-10", "section_hierarchy": ["INTRODUCTION"], "chunk_type": "section", "metadata": {"page_number": 10, "section_path": "INTRODUCTION", "content_lines": 1, "merged": true}, "token_count": 356, "semantic_summary": "SCANJET model SC90T2 is a tank-cleaning machine specially developed for cleaning of cargo and slop tanks onboard crude oil carriers. The washing unit is tailor-made for each specific tank."}, {"chunk_id": "chunk_0041", "title": "Table from Document", "content": "Table ID: table_10_0\n\n| IMPORTANT!\nNOTE! When handling the tankcleaning machine never lift the machine by\nthe nozzletube.\nNOTE! The use of tankcleaning machine may cause static electricity why\nmeasures must be taken in order to avoid sparks and possible explosion by\nhaving the tanks inert at time of cleaning.\nNOTE! Flush the supply line carefully before connecting the tankcleaning\nmachine for main supply. Scanjet will not take any responsibility for rough dirt\nand particles in the supply line, causing malfunction to the machine. |  |\n| --- | --- |\n|  |  |\n|  |  |\n|  |  |", "page_range": "10", "section_hierarchy": ["INSTALLATION GUIDE"], "chunk_type": "table", "metadata": {"page_number": 10, "table_id": "table_10_0", "table_rows": 4, "table_cols": 2}, "token_count": 138, "semantic_summary": "Tabular data with 4 rows"}, {"chunk_id": "chunk_0042", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 4 (36)\n# SC 90T2-CRUDE-07", "page_range": "11", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 11, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 4 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0043", "title": "WORKING PRINCIPE", "content": "WORKING PRINCIPE\nThe cleaning media comes from the supply line onboard\nthe vessel and enter into the inlet housing and passes the\nvertical turbine, which drives the driving unit.\nThe cleaning media continue through the main pipe to\nthe nozzles and then out in the tank.\nThe driving unit will rotate the main pipe and elevate the\nnozzle and will hereby clean the tank in a spherical\npattern. The rotation speed is controlled by the rotation\nspeed of the turbine and could easily be set to desired\nspeed.\nThe rotation of the main pipe and the elevation of nozzle\nare indicated on the scale on the lifting rod.\nThe elevation per revolution (Pitch) for the nozzle can be\nset to different pre-programmed values by means of push\nor pull the program knob. (See below).\nDirection of\nnozzle\nUp disengage\nDown engage\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "11", "section_hierarchy": ["WORKING PRINCIPE"], "chunk_type": "section", "metadata": {"page_number": 11, "section_path": "WORKING PRINCIPE", "content_lines": 28}, "token_count": 248, "semantic_summary": "The cleaning media comes from the supply line onboard the vessel and enters into the inlet housing. It passes the vertically driven turbine, which drives the driving unit. The driving unit will rotate the main pipe and elevate the nozzle. The rotation speed is controlled by the rotation speed of the turbine."}, {"chunk_id": "chunk_0044", "title": "Table from Document", "content": "Table ID: table_11_0\n\n| Direction o\nnozzle |\n| --- |\n|  |\n| Up disengag\nDown engag |\n|  |", "page_range": "11", "section_hierarchy": ["WORKING PRINCIPE"], "chunk_type": "table", "metadata": {"page_number": 11, "table_id": "table_11_0", "table_rows": 4, "table_cols": 1}, "token_count": 35, "semantic_summary": "Tabular data with 4 rows"}, {"chunk_id": "chunk_0045", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 5 (36)\n# SC 90T2-CRUDE-07", "page_range": "12", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 12, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 5 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0046", "title": "TECHNICAL DATA", "content": "TECHNICAL DATA\n\nSupply data: Scanjet SC 90T2 is operated with a supply of cleaning media at a flow of\napprox., 60-120 m³/h and at an inlet pressure of 0.6-1.2 MPa.\nRotating Speed: The rotation speed can vary between 0,5-1,5 rpm, depending on the supply\ndata and settings. The rotation speed can also vary with climate. In cold climate the machine\nmight rotate slower.", "page_range": "12-12", "section_hierarchy": ["TECHNICAL DATA"], "chunk_type": "section", "metadata": {"page_number": 12, "section_path": "TECHNICAL DATA", "content_lines": 1, "merged": true}, "token_count": 103, "semantic_summary": "TECHNICAL DATA\n\nSupply data: Scanjet SC 90T2 is operated with a supply of cleaning media at a flow of\napprox. , 60-120 m³/h and at an inlet pressure of 0."}, {"chunk_id": "chunk_0048", "title": "Weight: Machine with 1 m length of main pipe Approx. 62kg", "content": "Weight: Machine with 1 m length of main pipe Approx. 62kg\nPer additional meter of main pipe Approx. 15kg\nDriving unit Approx. 12kg.\n\nMaterial: Makers standard", "page_range": "12-12", "section_hierarchy": ["TECHNICAL DATA", "Weight: Machine with 1 m length of main pipe Approx. 62kg"], "chunk_type": "section", "metadata": {"page_number": 12, "section_path": "TECHNICAL DATA > Weight: Machine with 1 m length of main pipe Approx. 62kg", "content_lines": 3, "merged": true}, "token_count": 42, "semantic_summary": "Weight: Machine with 1 m length of main pipe Approx.  62kg\nPer additional meter of main pipe Approx."}, {"chunk_id": "chunk_0050", "title": "Performance data: The table below shows the flow and jet length for each combination of", "content": "Performance data: The table below shows the flow and jet length for each combination of\ninlet pressure and nozzle diameter.\nSupply Nozzle dia Nozzle dia Nozzle dia Nozzle dia Nozzle dia Nozzle dia\npressure Ø 20 mm Ø 22 mm Ø 24 mm Ø 26 mm Ø 28 mm Ø 29 mm\nMPa Flow Jet Flow Jet Flow Jet Flow Jet Flow Jet Flow Jet\nm³/h length m³/h length m³/h length m³/h length m³/h length m³/h length\nm m m m m m\n0,6 40 30 46 30 53 31 61 32 69 33 72 33\n0,8 46 32 53 32 61 33 70 34 79 35 82 35\n1,0 51 35 59 35 68 36 78 36 88 38 92 38\n1,2 55 37 65 37 74 38 86 38 96 40 100 40\nSupply Nozzle dia Nozzle dia Nozzle dia\npressure Ø 30 mm Ø 32 mm Ø 34 mm\nMPa Flow Jet Flow Jet Flow Jet\nm³/h Length m³/h Length m³/h Length\nm m m\n0,6 77 33 83 33 89 32\n0,8 87 35 95 35 103 34\n1,0 97 38 105 38 114 37\n1,2 106 40 114 40 125 39\nPreliminary effective Jet length. Other nozzles and maximum Jet Length available upon\nrequest.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "12", "section_hierarchy": ["TECHNICAL DATA", "Performance data: The table below shows the flow and jet length for each combination of"], "chunk_type": "section", "metadata": {"page_number": 12, "section_path": "TECHNICAL DATA > Performance data: The table below shows the flow and jet length for each combination of", "content_lines": 30}, "token_count": 434, "semantic_summary": "Preliminary effective Jet length. Other nozzles and maximum Jet Length available upon request. Performance data: The table below shows the flow and jet length for each combination of pressure and nozzle diameter."}, {"chunk_id": "chunk_0051", "title": "Table from TECHNICAL DATA > Performance data: The table below shows the flow and jet length for each combination of", "content": "Table ID: table_12_0\n\n| Supply\npressure\nMPa | Nozzle dia\nØ 20 mm |  | Nozzle dia\nØ 22 mm |  | Nozzle dia\nØ 24 mm |  | Nozzle dia\nØ 26 mm |  | Nozzle dia\nØ 28 mm |  | Nozzle dia\nØ 29 mm |  |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n|  | Flow\nm³/h | Jet\nlength\nm | Flow\nm³/h | Jet\nlength\nm | Flow\nm³/h | Jet\nlength\nm | Flow\nm³/h | Jet\nlength\nm | Flow\nm³/h | Jet\nlength\nm | Flow\nm³/h | Jet\nlength\nm |\n| 0,6 | 40 | 30 | 46 | 30 | 53 | 31 | 61 | 32 | 69 | 33 | 72 | 33 |\n| 0,8 | 46 | 32 | 53 | 32 | 61 | 33 | 70 | 34 | 79 | 35 | 82 | 35 |\n| 1,0 | 51 | 35 | 59 | 35 | 68 | 36 | 78 | 36 | 88 | 38 | 92 | 38 |\n| 1,2 | 55 | 37 | 65 | 37 | 74 | 38 | 86 | 38 | 96 | 40 | 100 | 40 |", "page_range": "12", "section_hierarchy": ["TECHNICAL DATA", "Performance data: The table below shows the flow and jet length for each combination of"], "chunk_type": "table", "metadata": {"page_number": 12, "table_id": "table_12_0", "table_rows": 6, "table_cols": 13}, "token_count": 359, "semantic_summary": "Tabular data with 6 rows"}, {"chunk_id": "chunk_0052", "title": "Table from TECHNICAL DATA > Performance data: The table below shows the flow and jet length for each combination of", "content": "Table ID: table_12_1\n\n| Supply\npressure\nMPa | Nozzle dia\nØ 30 mm |  | Nozzle dia\nØ 32 mm |  | Nozzle dia\nØ 34 mm |  |\n| --- | --- | --- | --- | --- | --- | --- |\n|  | Flow\nm³/h | Jet\nLength\nm | Flow\nm³/h | Jet\nLength\nm | Flow\nm³/h | Jet\nLength\nm |\n| 0,6 | 77 | 33 | 83 | 33 | 89 | 32 |\n| 0,8 | 87 | 35 | 95 | 35 | 103 | 34 |\n| 1,0 | 97 | 38 | 105 | 38 | 114 | 37 |\n| 1,2 | 106 | 40 | 114 | 40 | 125 | 39 |", "page_range": "12", "section_hierarchy": ["TECHNICAL DATA", "Performance data: The table below shows the flow and jet length for each combination of"], "chunk_type": "table", "metadata": {"page_number": 12, "table_id": "table_12_1", "table_rows": 6, "table_cols": 7}, "token_count": 203, "semantic_summary": "Tabular data with 6 rows"}, {"chunk_id": "chunk_0053", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 6 (36)\n# SC 90T2-CRUDE-07", "page_range": "13", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 13, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 6 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0054", "title": "GENERAL INSTALLATION DATA", "content": "GENERAL INSTALLATION DATA\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "13", "section_hierarchy": ["GENERAL INSTALLATION DATA"], "chunk_type": "section", "metadata": {"page_number": 13, "section_path": "GENERAL INSTALLATION DATA", "content_lines": 9}, "token_count": 75, "semantic_summary": "GENERAL INSTALLATION DATA\nScanjet Marine AB\nSödra Långebergsgatan 36\nP. O."}, {"chunk_id": "chunk_0055", "title": "Table from Document", "content": "Table ID: table_13_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "13", "section_hierarchy": ["GENERAL INSTALLATION DATA"], "chunk_type": "table", "metadata": {"page_number": 13, "table_id": "table_13_0", "table_rows": 32, "table_cols": 1}, "token_count": 108, "semantic_summary": "Tabular data with 32 rows"}, {"chunk_id": "chunk_0056", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 7 (36)\n# SC 90T2-CRUDE-07\nDeck flange\nDeck flange No. A1 B1 C1\nPN16 DN150 90378-02 Ø285 Ø240 Ø23x8\nANSI 6” 150 lb 90378-15 Ø279 Ø241 Ø23x8\nInlet flange\nInlet house No. A2 B2 C2\nPN16 DN 80 90379-04 Ø200 Ø160 Ø18x8\nJIS 16K 80A 90379-10 Ø200 Ø160 Ø23x8\nANSI 3” 150lb 90379-23 Ø190.5 Ø152.4 Ø19x4\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "14", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 14, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 19}, "token_count": 228, "semantic_summary": "Page 7 (36)\n# SC 90T2-CRUDE-07\nDeck flange\nDeck flange No.  A1 B1 C1\nPN16 DN150 90378-02 Ø285 Ø240 Ø23x8\nANSI 6” 150 lb 90378-15 Ø279 Ø241 Ø23x8\nInlet flange\nInlet house No."}, {"chunk_id": "chunk_0057", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_14_0\n\n|  |\n| --- |\n|  |\n\nTable ID: table_14_1\n\n| Deck flange |  |  |  |  |\n| --- | --- | --- | --- | --- |\n|  | Deck flange No. | A1 | B1 | C1 |\n| PN16 DN150 | 90378-02 | Ø285 | Ø240 | Ø23x8 |\n| ANSI 6” 150 lb | 90378-15 | Ø279 | Ø241 | Ø23x8 |", "page_range": "14-14", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 14, "table_id": "table_14_0", "table_rows": 2, "table_cols": 1, "merged": true}, "token_count": 118, "semantic_summary": "Table ID: table_14_0\n\n|  |\n| --- |\n|  |\n\nTable ID: table_14_1\n\n| Deck flange |  |  |  |  |\n| --- | --- | --- | --- | --- |\n|  | Deck flange No.  | A1 | B1 | C1 |\n| PN16 DN150 | 90378-02 | Ø285 | Ø240 | Ø23x8 |\n| ANSI 6” 150 lb | 90378-15 | Ø279 | Ø241 | Ø23x8 |."}, {"chunk_id": "chunk_0059", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_14_2\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_14_3\n\n| Inlet flange |  |  |  |  |\n| --- | --- | --- | --- | --- |\n|  | Inlet house No. | A2 | B2 | C2 |\n| PN16 DN 80 | 90379-04 | Ø200 | Ø160 | Ø18x8 |\n| JIS 16K 80A | 90379-10 | Ø200 | Ø160 | Ø23x8 |\n| ANSI 3” 150lb | 90379-23 | Ø190.5 | Ø152.4 | Ø19x4 |", "page_range": "14-14", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 14, "table_id": "table_14_2", "table_rows": 6, "table_cols": 1, "merged": true}, "token_count": 163, "semantic_summary": "Table ID: table_14_2\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_14_3\n\n| Inlet flange |  |  |  |  |\n| --- | --- | --- | --- | --- |\n|  | Inlet house No.  | A2 | B2 | C2 |\n| PN16 DN 80 | 90379-04 | Ø200 | Ø160 | Ø18x8 |\n| JIS 16K 80A | 90379-10 | Ø200 | Ø160 | Ø23x8 |\n| ANSI 3” 150lb | 90379-23 | Ø190."}, {"chunk_id": "chunk_0061", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 8 (36)\n# SC 90T2-CRUDE-07", "page_range": "15", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 15, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 8 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0062", "title": "OPERATION", "content": "OPERATION\nStarting up\n\n1. Remove protective cover. Remove the drainage plug under the driving unit\nand drain it.", "page_range": "15-15", "section_hierarchy": ["OPERATION"], "chunk_type": "section", "metadata": {"page_number": 15, "section_path": "OPERATION", "content_lines": 2, "merged": true}, "token_count": 25, "semantic_summary": "OPERATION\nStarting up\n\n1.  Remove protective cover."}, {"chunk_id": "chunk_0064", "title": "2. Pull up all program knobs, check that the machine could be hand cranked", "content": "2. Pull up all program knobs, check that the machine could be hand cranked\none full cycle. Set nozzles to desired starting point by using the hand-\nmanoeuvring device and remove the inspection plug (A) at the cofferdam.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "15", "section_hierarchy": ["2. Pull up all program knobs, check that the machine could be hand cranked"], "chunk_type": "section", "metadata": {"page_number": 15, "section_path": "2. Pull up all program knobs, check that the machine could be hand cranked", "content_lines": 11}, "token_count": 124, "semantic_summary": "2.  Pull up all program knobs, check that the machine could be hand cranked\none full cycle."}, {"chunk_id": "chunk_0065", "title": "Table from Document", "content": "Table ID: table_15_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_15_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "15-15", "section_hierarchy": ["2. Pull up all program knobs, check that the machine could be hand cranked"], "chunk_type": "table", "metadata": {"page_number": 15, "table_id": "table_15_0", "table_rows": 9, "table_cols": 1, "merged": true}, "token_count": 90, "semantic_summary": "Table ID: table_15_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_15_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0067", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 9 (36)\n# SC 90T2-CRUDE-07", "page_range": "16", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 16, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 9 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0068", "title": "3. Open the inlet valve slowly to start the machine.", "content": "3. Open the inlet valve slowly to start the machine.\nNOTE! IF THE MACHINE IS STARTED TO FAST, THE MAGNETIC\n\nCOUPLING WILL RELEASE AND THE VALVE MUST BE\nCOMPLETELY CLOSED PRIOR TO RESTART.", "page_range": "16-16", "section_hierarchy": ["3. Open the inlet valve slowly to start the machine."], "chunk_type": "section", "metadata": {"page_number": 16, "section_path": "3. Open the inlet valve slowly to start the machine.", "content_lines": 2, "merged": true}, "token_count": 50, "semantic_summary": "3.  Open the inlet valve slowly to start the machine."}, {"chunk_id": "chunk_0070", "title": "4. Set desired program by pulling or pressing down the program knob. See next", "content": "4. Set desired program by pulling or pressing down the program knob. See next\npage for programming instructions\nUp disengage\nDown engage\n\n5. Remount the inspection plug (A) when the cleaning is finished. Handcrank\nthe machine to zero degrees, this will drain it completely\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "16-16", "section_hierarchy": ["4. Set desired program by pulling or pressing down the program knob. See next"], "chunk_type": "section", "metadata": {"page_number": 16, "section_path": "4. Set desired program by pulling or pressing down the program knob. See next", "content_lines": 4, "merged": true}, "token_count": 131, "semantic_summary": "4.  Set desired program by pulling or pressing down the program knob."}, {"chunk_id": "chunk_0072", "title": "Table from Document", "content": "Table ID: table_16_0\n\n|  |\n| --- |\n| Up disengage |\n| Down engage |\n|  |\n\nTable ID: table_16_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "16-16", "section_hierarchy": ["5. Remount the inspection plug (A) when the cleaning is finished. Handcrank"], "chunk_type": "table", "metadata": {"page_number": 16, "table_id": "table_16_0", "table_rows": 4, "table_cols": 1, "merged": true}, "token_count": 78, "semantic_summary": "Table ID: table_16_0\n\n|  |\n| --- |\n| Up disengage |\n| Down engage |\n|  |\n\nTable ID: table_16_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0074", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 10 (36)\n# SC 90T2-CRUDE-07\nSetting Programs\nThe standard drive unit is delivered with four pre-setted programs giving the following\ndegrees of vertical elevation per revolution of main pipe (pitch).\nNOTE! Pre-setted programs may vary from below spec. Please check your \"White board for\ntankcleaning\" for programs installed on your vessel.\nAction Standard Optional\nElevation Elevation\nAll program knobs in upper position 0 0\nOne program knob pushed down 1,5°/rev 2,5°/rev\nTwo program knobs pushed down 3,0°/rev 5,0°/rev\nThree program knobs pushed down 4,5°/rev 7,5°/rev\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "17", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 17, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 21}, "token_count": 230, "semantic_summary": "The standard drive unit is delivered with four pre-setted programs giving the followingdegrees of vertical elevation per revolution of main pipe (pitch) Pre-setting programs may vary from below spec. Please check your \"White board fortankcleaning\" for programs installed on your vessel."}, {"chunk_id": "chunk_0075", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_17_0\n\n| Action | Standard\nElevation | Optional\nElevation |\n| --- | --- | --- |\n| All program knobs in upper position | 0 | 0 |\n| One program knob pushed down | 1,5°/rev | 2,5°/rev |\n| Two program knobs pushed down | 3,0°/rev | 5,0°/rev |\n| Three program knobs pushed down | 4,5°/rev | 7,5°/rev |\n\nTable ID: table_17_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "17-17", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 17, "table_id": "table_17_0", "table_rows": 5, "table_cols": 3, "merged": true}, "token_count": 163, "semantic_summary": "Table ID: table_17_0\n\n| Action | Standard\nElevation | Optional\nElevation |\n| --- | --- | --- |\n| All program knobs in upper position | 0 | 0 |\n| One program knob pushed down | 1,5°/rev | 2,5°/rev |\n| Two program knobs pushed down | 3,0°/rev | 5,0°/rev |\n| Three program knobs pushed down | 4,5°/rev | 7,5°/rev |\n\nTable ID: table_17_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0077", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 11 (36)\n# SC 90T2-CRUDE-07\nCalculation of cleaning time\nA) Calculation of cleaning time for a cycle", "page_range": "18", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 18, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 4}, "token_count": 33, "semantic_summary": "Page 11 (36)\n# SC 90T2-CRUDE-07\nCalculation of cleaning time\nA) Calculation of cleaning time for a cycle."}, {"chunk_id": "chunk_0078", "title": "The cleaning time depends of the following:", "content": "The cleaning time depends of the following:\nChoose program with its characteristic pitch Angle:\nA (elevation/ rev.)\nRotation speed of main pipe (indicated on the lifting rod on top of\nthe machine).\nB (sec/rev.)\n\nWashing angle:\nC (degrees)", "page_range": "18-18", "section_hierarchy": ["Including: Jinling Shipyard", "The cleaning time depends of the following:"], "chunk_type": "section", "metadata": {"page_number": 18, "section_path": "Including: Jinling Shipyard > The cleaning time depends of the following:", "content_lines": 6, "merged": true}, "token_count": 56, "semantic_summary": "The cleaning time depends of the following:\nChoose program with its characteristic pitch Angle:\nA (elevation/ rev. )\nRotation speed of main pipe (indicated on the lifting rod on top of\nthe machine)."}, {"chunk_id": "chunk_0080", "title": "Cleaning time:", "content": "Cleaning time:\nD (minutes)\n\nCleaning time:\nD=(CxB)/(Ax60)\nExample 1: Three program knobs are pushed down and one turn of main pipe takes 60 sec\n(measured with a wrist watch by checking time for one turn of the lifting rod). How long time\ndoes it take to wash the tank (one full cycle)?\nA=Pitch 3x1.5º=4.5º\nB=60 sec/rev\nC=(0-180º) or (180º-0º)\nCleaning time D=(180ºx60)/(4,5ºx60)=40min\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "18-18", "section_hierarchy": ["Including: Jinling Shipyard", "Cleaning time:"], "chunk_type": "section", "metadata": {"page_number": 18, "section_path": "Including: Jinling Shipyard > Cleaning time:", "content_lines": 2, "merged": true}, "token_count": 199, "semantic_summary": "How long does it take to wash the tank (one full cycle)? A=Pitch 3x1.5º=4.5°B=60 sec/rev. C=(0-180º) or (180º-0º)Cleaning time D=(180ºx60)/(4,5°x60)=40min."}, {"chunk_id": "chunk_0082", "title": "Table from Including: Jinling Shipyard > Cleaning time:", "content": "Table ID: table_18_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "18", "section_hierarchy": ["Including: Jinling Shipyard", "Cleaning time:"], "chunk_type": "table", "metadata": {"page_number": 18, "table_id": "table_18_0", "table_rows": 19, "table_cols": 1}, "token_count": 69, "semantic_summary": "Tabular data with 19 rows"}, {"chunk_id": "chunk_0083", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 12 (36)\n# SC 90T2-CRUDE-07\nExample 2: Commercial cleaning two program knobs has been\npressed down. We would like to do a cleaning between 120 degrees\nand 45 degrees. How long time does it take?\nA = 3.0 degrees/rev\nB = 50 sec/rev\nC = 75 degrees\nCleaning time D=(75x50)/(3.0x60)=~21 minutes\nB) Calculation of cleaning time for getting out a certain amount of\ncleaning media (prewash).\npage 6.\nT=R/Q*60 (min)\nExample 3:\npressure.\nSolution\nNeeded time T=12/78*60=9,5 min\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "19", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 19, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 25}, "token_count": 225, "semantic_summary": "Cleaning time D=(75x50)/(3.0x60)=~21 minutes. Calculation of cleaning time for getting out a certain amount of cleaning media (prewash)"}, {"chunk_id": "chunk_0084", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 13 (36)\n# SC 90T2-CRUDE-07\nWaterflow for different nozzle sizes at\nspecific inlet pressure\n127\n123\n119\n115\n111\n107\n103\n99\n95\n91\n87\n83\n79\n75\n71\n67\n63\n59\n55\n51\n47\n43\n39\n35\n5 6 7 8 9 10 11 12 13\nPressure (bar)\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se\n)h/3^m(\nwolF\nØ 34\nØ 32\nØ 30\nØ 28\nØ 26\nØ 24\nØ 22\nØ 20", "page_range": "20", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 20, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 48}, "token_count": 221, "semantic_summary": "Page 13 (36)\n# SC 90T2-CRUDE-07\nWaterflow for different nozzle sizes at\nspecific inlet pressure\n127\n123\n119\n115\n111\n107\n103\n99\n95\n91\n87\n83\n79\n75\n71\n67\n63\n59\n55\n51\n47\n43\n39\n35\n5 6 7 8 9 10 11 12 13\nPressure (bar)\nScanjet Marine AB\nSödra Långebergsgatan 36\nP. O."}, {"chunk_id": "chunk_0085", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_20_0\n\n|  | Ø 34 |  |  |  |  |  |\n| --- | --- | --- | --- | --- | --- | --- |\n|  | Ø 32 |  |  |  |  |  |\n|  | Ø 30\nØ 28 |  |  |  |  |  |\n|  | Ø 26 |  |  |  |  |  |\n|  | Ø 24 |  |  |  |  |  |\n|  | Ø 22 |  |  |  |  |  |\n|  | Ø 20 |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |", "page_range": "20", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 20, "table_id": "table_20_0", "table_rows": 26, "table_cols": 7}, "token_count": 433, "semantic_summary": "Tabular data with 26 rows"}, {"chunk_id": "chunk_0086", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 14 (36)\n# SC 90T2-CRUDE-07\nStandard\nCleaning time for full cycle (180°) at different cleaning programs\ndepending on the rotation speed for the main pipe\n200\n175\n150\n125\n100\n75\n50\n25\n0\n0,6 0,8 1 1,2 1,4 1,6 1,8 2\nRotation speed of gun (r/min)\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se\n)nim(\nemit\nelcyC\nP1=1,5°/rev\nP2=3,0°/rev\nP3=4,5°/rev\nOptional\nCleaning time for full cycle (180°) at different cleaning programs\ndepending on the rotation speed for the main pipe\n100\n90\n80\n70\n60\n50\n40\n30\n20\n10\n0\n0,6 0,8 1 1,2 1,4 1,6 1,8 2\nRotation speed of gun (r/min)\n)nim(\nemit\nelcyC\nP1=2,5°/rev\nP2=5,0°/rev\nP3=7,5°/rev", "page_range": "21", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 21, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 52}, "token_count": 330, "semantic_summary": "Cleaning time for full cycle (180°) at different cleaning programs. Depending on the rotation speed for the main pipe. Rotation speed of gun (r/min)"}, {"chunk_id": "chunk_0087", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_21_0\n\n|  |  | P1=1,5°/rev |  |\n| --- | --- | --- | --- |\n|  |  | P2=3,0°/rev |  |\n|  |  | P3=4,5°/rev |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n\nTable ID: table_21_1\n\n|  |  |  |  | P1=2,5°/rev |  |\n| --- | --- | --- | --- | --- | --- |\n|  |  |  |  |  |  |\n|  |  |  |  | P2=5,0°/rev |  |\n|  |  |  |  | P3=7,5°/rev |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |", "page_range": "21-21", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 21, "table_id": "table_21_0", "table_rows": 8, "table_cols": 4, "merged": true}, "token_count": 290, "semantic_summary": "Table ID: table_21_0\n\n|  |  | P1=1,5°/rev |  |\n| --- | --- | --- | --- |\n|  |  | P2=3,0°/rev |  |\n|  |  | P3=4,5°/rev |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n\nTable ID: table_21_1\n\n|  |  |  |  | P1=2,5°/rev |  |\n| --- | --- | --- | --- | --- | --- |\n|  |  |  |  |  |  |\n|  |  |  |  | P2=5,0°/rev |  |\n|  |  |  |  | P3=7,5°/rev |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |\n|  |  |  |  |  |  |."}, {"chunk_id": "chunk_0089", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 15 (36)\n# SC 90T2-CRUDE-07\nSpeed Adjustment\nChanging the rotating speed for the turbine sets the rotating speed of the main pipe. This is\ndone by changing position of the conical turbine in its sleeve. The speed can be adjusted while\noperating the machine by doing the following: Note that the machine might rotate slower in\ncold climate.", "page_range": "22", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 22, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 7}, "token_count": 81, "semantic_summary": "Page 15 (36)\n# SC 90T2-CRUDE-07\nSpeed Adjustment\nChanging the rotating speed for the turbine sets the rotating speed of the main pipe.  This is\ndone by changing position of the conical turbine in its sleeve."}, {"chunk_id": "chunk_0090", "title": "1. Look at the top nut (pos 103) through the cofferdam hole (E). The time it", "content": "1. Look at the top nut (pos 103) through the cofferdam hole (E). The time it\ntakes for one turn can be measured with a wristwatch.\n\n2. The speed should be 0,5-1.5 rpm if not, adjust as followed.", "page_range": "22-22", "section_hierarchy": ["1. Look at the top nut (pos 103) through the cofferdam hole (E). The time it"], "chunk_type": "section", "metadata": {"page_number": 22, "section_path": "1. Look at the top nut (pos 103) through the cofferdam hole (E). The time it", "content_lines": 2, "merged": true}, "token_count": 59, "semantic_summary": "1.  Look at the top nut (pos 103) through the cofferdam hole (E)."}, {"chunk_id": "chunk_0092", "title": "3. Remove protective cup (A) (Key No. 25).", "content": "3. Remove protective cup (A) (Key No. 25).\n\n4. Loosen contra nut (B) (Key No. 30).\nStop the machine while adjusting the screw, and start again when the contra nut are tightened.\nCheck the speed, if not ok adjust again.", "page_range": "22-22", "section_hierarchy": ["3. Remove protective cup (A) (Key No. 25)."], "chunk_type": "section", "metadata": {"page_number": 22, "section_path": "3. Remove protective cup (A) (Key No. 25).", "content_lines": 1, "merged": true}, "token_count": 59, "semantic_summary": "3.  Remove protective cup (A) (Key No."}, {"chunk_id": "chunk_0094", "title": "5. Set the adjusting screw (D) to get the desired speed by using an Allen key", "content": "5. Set the adjusting screw (D) to get the desired speed by using an Allen key\nNo. 6. By lifting the adjusting screw (D) the speed will reduce and vice\nverse. The fastest speed that is possible to get is when the turbine is running\nwith a minimal gap between the turbine and the sleeve. This is achieved by\nlowering the turbine until it stops and then raise it approx. ½ a turn.\n\n6. After the speed has been set, tighten contra nut (B).", "page_range": "22-22", "section_hierarchy": ["5. Set the adjusting screw (D) to get the desired speed by using an Allen key"], "chunk_type": "section", "metadata": {"page_number": 22, "section_path": "5. Set the adjusting screw (D) to get the desired speed by using an Allen key", "content_lines": 5, "merged": true}, "token_count": 106, "semantic_summary": "5.  Set the adjusting screw (D) to get the desired speed by using an Allen key\nNo."}, {"chunk_id": "chunk_0096", "title": "7. Recheck the speed of the machine step 1-2 and if OK check for leakages at", "content": "7. Recheck the speed of the machine step 1-2 and if OK check for leakages at\nthe adjusting screw. If leakages occur change O-ring (C) (Pos. 142).\n\n8. Replace the protective cap (A).\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "22-22", "section_hierarchy": ["7. Recheck the speed of the machine step 1-2 and if OK check for leakages at"], "chunk_type": "section", "metadata": {"page_number": 22, "section_path": "7. Recheck the speed of the machine step 1-2 and if OK check for leakages at", "content_lines": 2, "merged": true}, "token_count": 122, "semantic_summary": "7.  Recheck the speed of the machine step 1-2 and if OK check for leakages at\nthe adjusting screw."}, {"chunk_id": "chunk_0098", "title": "Table from Document", "content": "Table ID: table_22_0\n\n|  |  |  |\n| --- | --- | --- |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |", "page_range": "22", "section_hierarchy": ["8. Replace the protective cap (A)."], "chunk_type": "table", "metadata": {"page_number": 22, "table_id": "table_22_0", "table_rows": 8, "table_cols": 3}, "token_count": 72, "semantic_summary": "Tabular data with 8 rows"}, {"chunk_id": "chunk_0099", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 16 (36)\n# SC 90T2-CRUDE-07\nDuring operation\nDuring the cleaning operation the machine should be inspected\nand checked for leakage by opening inspection cover in\ncofferdam (A). Make sure that the machine operates, as it should\nby looking through the cofferdam hole (A) to see that the top nut\nrotates with correct speed approx. 1-1,6 rpm. Also check that the\nlifting rod is rotating and elevating (B).\nIf leakage is detected the sealings inside the machine has to be\nchanged.\nNote! That leakage at the nozzle house and at the pipe support is\nnormal and necessary to flush the Teflon bearings. During cold\nwater rinsing the leakage is considerably higher then during hot\nwater rinsing.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "23", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 23, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 23}, "token_count": 237, "semantic_summary": "During cleaning the machine should be inspected and checked for leakage. Leakage at the nozzle house and at the pipe support is normal and necessary to flush the Teflon bearings. During cold water rinsing the leakage is considerably higher then during hot water. Check that the lifting rod is rotating and elevating."}, {"chunk_id": "chunk_0100", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_23_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_23_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "23-23", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 23, "table_id": "table_23_0", "table_rows": 12, "table_cols": 1, "merged": true}, "token_count": 87, "semantic_summary": "Table ID: table_23_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_23_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0102", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 17 (36)\n# SC 90T2-CRUDE-07\nClosing down\nWhen the cleaning operation is finalised the following should be done.", "page_range": "24", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 24, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 4}, "token_count": 34, "semantic_summary": "Page 17 (36)\n# SC 90T2-CRUDE-07\nClosing down\nWhen the cleaning operation is finalised the following should be done.."}, {"chunk_id": "chunk_0103", "title": "1. Close the main valve that will end the cleaning operation and stop the drive", "content": "1. Close the main valve that will end the cleaning operation and stop the drive\nunit.\n\n2. Make sure that the machine is left with the nozzle pointing\ndownwards as this automatically drains the main pipe and ensures that\nno water will be left in the unit.\nThe nozzle is pointing downwards when the lifting rod is in its lowest\nposition. If necessary the nozzle should be hand manoeuvred by using\nthe hand cranking device to get the nozzle pointing downwards and remount\ninspection plug (A).", "page_range": "24-24", "section_hierarchy": ["1. Close the main valve that will end the cleaning operation and stop the drive"], "chunk_type": "section", "metadata": {"page_number": 24, "section_path": "1. Close the main valve that will end the cleaning operation and stop the drive", "content_lines": 2, "merged": true}, "token_count": 104, "semantic_summary": "1.  Close the main valve that will end the cleaning operation and stop the drive\nunit."}, {"chunk_id": "chunk_0105", "title": "Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content": "Note: Before hand cranking the drive unit disconnect programs by pulling up all\nprogram knobs.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "24", "section_hierarchy": ["2. Make sure that the machine is left with the nozzle pointing", "Note: Before hand cranking the drive unit disconnect programs by pulling up all"], "chunk_type": "section", "metadata": {"page_number": 24, "section_path": "2. Make sure that the machine is left with the nozzle pointing > Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content_lines": 10}, "token_count": 89, "semantic_summary": "Note: Before hand cranking the drive unit disconnect programs by pulling up all\nprogram knobs. \nScanjet Marine AB\nSödra Långebergsgatan 36\nP."}, {"chunk_id": "chunk_0106", "title": "Table from 2. Make sure that the machine is left with the nozzle pointing > Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content": "Table ID: table_24_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_24_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "24-24", "section_hierarchy": ["2. Make sure that the machine is left with the nozzle pointing", "Note: Before hand cranking the drive unit disconnect programs by pulling up all"], "chunk_type": "table", "metadata": {"page_number": 24, "table_id": "table_24_0", "table_rows": 13, "table_cols": 1, "merged": true}, "token_count": 102, "semantic_summary": "Table ID: table_24_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_24_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0108", "title": "Table from 2. Make sure that the machine is left with the nozzle pointing > Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content": "Table ID: table_24_2\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "24", "section_hierarchy": ["2. Make sure that the machine is left with the nozzle pointing", "Note: Before hand cranking the drive unit disconnect programs by pulling up all"], "chunk_type": "table", "metadata": {"page_number": 24, "table_id": "table_24_2", "table_rows": 16, "table_cols": 1}, "token_count": 60, "semantic_summary": "Tabular data with 16 rows"}, {"chunk_id": "chunk_0109", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 18 (36)\n# SC 90T2-CRUDE-07", "page_range": "25", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 25, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 18 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0110", "title": "MAINTENANCE", "content": "MAINTENANCE\n\nRegular inspection before operation:\nis pressurised. If leakage is detected the sealings have to be changed, the driving unit also\nhave to be checked for leakage and drained. If water is trapped inside the driving unit it will\nbe damaged.", "page_range": "25-25", "section_hierarchy": ["MAINTENANCE"], "chunk_type": "section", "metadata": {"page_number": 25, "section_path": "MAINTENANCE", "content_lines": 1, "merged": true}, "token_count": 55, "semantic_summary": "MAINTENANCE\n\nRegular inspection before operation:\nis pressurised.  If leakage is detected the sealings have to be changed, the driving unit also\nhave to be checked for leakage and drained."}, {"chunk_id": "chunk_0112", "title": "Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content": "Note: Before hand cranking the drive unit disconnect programs by pulling up all\nprogram knobs.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "25", "section_hierarchy": ["MAINTENANCE", "Note: Before hand cranking the drive unit disconnect programs by pulling up all"], "chunk_type": "section", "metadata": {"page_number": 25, "section_path": "MAINTENANCE > Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content_lines": 10}, "token_count": 89, "semantic_summary": "Note: Before hand cranking the drive unit disconnect programs by pulling up all\nprogram knobs. \nScanjet Marine AB\nSödra Långebergsgatan 36\nP."}, {"chunk_id": "chunk_0113", "title": "Table from MAINTENANCE > Note: Before hand cranking the drive unit disconnect programs by pulling up all", "content": "Table ID: table_25_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "25", "section_hierarchy": ["MAINTENANCE", "Note: Before hand cranking the drive unit disconnect programs by pulling up all"], "chunk_type": "table", "metadata": {"page_number": 25, "table_id": "table_25_0", "table_rows": 13, "table_cols": 1}, "token_count": 51, "semantic_summary": "Tabular data with 13 rows"}, {"chunk_id": "chunk_0114", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 19 (36)\n# SC 90T2-CRUDE-07\nEvery 12 month or every 100h operation whichever comes first:", "page_range": "26", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 26, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 3}, "token_count": 32, "semantic_summary": "Page 19 (36)\n# SC 90T2-CRUDE-07\nEvery 12 month or every 100h operation whichever comes first:"}, {"chunk_id": "chunk_0115", "title": "Suitable grease is: Mobil OGL 007 or equal to this specification.", "content": "Suitable grease is: Mobil OGL 007 or equal to this specification.\nIf another type of grease is used, the gearbox has to be cleaned and all old grease must be\nremoved.\nnozzle house. Change if particles are stacked in the bearings or if bearings are worn out.\nNOTE! When replacing old/new bearings (161) at the nozzle house the bearing flange (160)\nshould be screwed in by hand to finger tight position and then unscrew ¼ -½ turn prior to fit\nin the split pin 159. This is important in order to get the correct clearance for flushing the\nbearing during the cleaning. When replacing the nozzle bearings also replace bearing pos.\n163.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "26", "section_hierarchy": ["Including: Jinling Shipyard", "Suitable grease is: Mobil OGL 007 or equal to this specification."], "chunk_type": "section", "metadata": {"page_number": 26, "section_path": "Including: Jinling Shipyard > Suitable grease is: Mobil OGL 007 or equal to this specification.", "content_lines": 17}, "token_count": 210, "semantic_summary": "If another type of grease is used, the gearbox has to be cleaned and all old grease must be removed. Suitable grease is: Mobil OGL 007 or equal to this specification."}, {"chunk_id": "chunk_0116", "title": "Table from Including: Jinling Shipyard > Suitable grease is: Mobil OGL 007 or equal to this specification.", "content": "Table ID: table_26_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_26_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |", "page_range": "26-26", "section_hierarchy": ["Including: Jinling Shipyard", "Suitable grease is: Mobil OGL 007 or equal to this specification."], "chunk_type": "table", "metadata": {"page_number": 26, "table_id": "table_26_0", "table_rows": 5, "table_cols": 1, "merged": true}, "token_count": 54, "semantic_summary": "Table ID: table_26_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n\nTable ID: table_26_1\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |."}, {"chunk_id": "chunk_0118", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 20 (36)\n# SC 90T2-CRUDE-07\nEvery 24 month or every 200h operation whichever comes first:\n– Check gears and bearings for wear if necessary replace.\n– Check the gearbox so it is properly greased. If necessary, refill.", "page_range": "27", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 27, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 5}, "token_count": 59, "semantic_summary": "Page 20 (36)\n# SC 90T2-CRUDE-07\nEvery 24 month or every 200h operation whichever comes first:\n– Check gears and bearings for wear if necessary replace. \n– Check the gearbox so it is properly greased."}, {"chunk_id": "chunk_0119", "title": "Suitable grease is: Mobil OGL 007 or equal to this specification.", "content": "Suitable grease is: Mobil OGL 007 or equal to this specification.\nIf another type of grease is used, the gearbox has to be cleaned and all old grease must be\nremoved.\n– Check and change bearings at nozzle house if needed.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "27", "section_hierarchy": ["Including: Jinling Shipyard", "Suitable grease is: Mobil OGL 007 or equal to this specification."], "chunk_type": "section", "metadata": {"page_number": 27, "section_path": "Including: Jinling Shipyard > Suitable grease is: Mobil OGL 007 or equal to this specification.", "content_lines": 12}, "token_count": 120, "semantic_summary": "Suitable grease is: Mobil OGL 007 or equal to this specification. \nIf another type of grease is used, the gearbox has to be cleaned and all old grease must be\nremoved."}, {"chunk_id": "chunk_0120", "title": "Table from Including: Jinling Shipyard > Suitable grease is: Mobil OGL 007 or equal to this specification.", "content": "Table ID: table_27_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |", "page_range": "27", "section_hierarchy": ["Including: Jinling Shipyard", "Suitable grease is: Mobil OGL 007 or equal to this specification."], "chunk_type": "table", "metadata": {"page_number": 27, "table_id": "table_27_0", "table_rows": 5, "table_cols": 1}, "token_count": 27, "semantic_summary": "Tabular data with 5 rows"}, {"chunk_id": "chunk_0121", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 21 (36)\n# SC 90T2-CRUDE-07\nremoving driving unit and open cover to magnetic transmission. Check the upper shaft (Pos.\nNOTE!\nProtect upper magnet\nfrom dirt and damage\nGrease thread with\nDEPAC antiseize grease\nUpper shaft (Pos. 127)\nLower shaft (Pos. 132)\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "28", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 28, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 18}, "token_count": 146, "semantic_summary": "Page 21 (36)\n# SC 90T2-CRUDE-07\nremoving driving unit and open cover to magnetic transmission.  Check the upper shaft (Pos."}, {"chunk_id": "chunk_0122", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_28_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n| NOTE! |\n| Protect upper magnet\nfrom dirt and damage |\n| Grease thread with\nDEPAC antiseize grease |\n|  |\n| Upper shaft (Pos. 127) |\n|  |\n|  |\n|  |\n| Lower shaft (Pos. 132) |\n|  |\n|  |\n|  |", "page_range": "28", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 28, "table_id": "table_28_0", "table_rows": 18, "table_cols": 1}, "token_count": 99, "semantic_summary": "Tabular data with 18 rows"}, {"chunk_id": "chunk_0123", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 22 (36)\n# SC 90T2-CRUDE-07\nAt major overhaul or time of drydocking of vessel.\nadding new grease.\n118, 120, 121, 123, 129, 132, 141, 142, 145, 149, 150, 172).\n9, 10, 17, 18, 22, 26, 35, 37, 39, 76, 77).\nfree flow and is properly attached.\nand the weldings for damages. If needed replace or send to workshop for refurbishment.\nare oval more than 2 mm send it to workshop for refurbishment.\n-\nAlways replace Pos. 153 and 155 when the lifting rod is removed from the main pipe\nCheck weldings\nand wear\nInspect wear\nCheck weldings\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "29", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 29, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 23}, "token_count": 246, "semantic_summary": "At major overhaul or time of drydocking of vessel. Add new grease. If needed replace or send to workshop for refurbishment. If oval more than 2 mm send it to workshop."}, {"chunk_id": "chunk_0124", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_29_0\n\n|  |\n| --- |\n|  |", "page_range": "29", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 29, "table_id": "table_29_0", "table_rows": 2, "table_cols": 1}, "token_count": 18, "semantic_summary": "Tabular data with 2 rows"}, {"chunk_id": "chunk_0125", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 23 (36)\n# SC 90T2-CRUDE-07", "page_range": "30", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 30, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 23 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0126", "title": "REMOVAL OF DRIVING UNIT", "content": "REMOVAL OF DRIVING UNIT\nTo remove the driving unit from the washing unit at time of service etc. the following\nprocedure should be followed:\n\n1. Remove the protective cover (A).", "page_range": "30-30", "section_hierarchy": ["REMOVAL OF DRIVING UNIT"], "chunk_type": "section", "metadata": {"page_number": 30, "section_path": "REMOVAL OF DRIVING UNIT", "content_lines": 3, "merged": true}, "token_count": 40, "semantic_summary": "REMOVAL OF DRIVING UNIT\nTo remove the driving unit from the washing unit at time of service etc.  the following\nprocedure should be followed:\n\n1."}, {"chunk_id": "chunk_0128", "title": "2. Hand crank the machine to the lowest position (0°).", "content": "2. Hand crank the machine to the lowest position (0°).\n\nNote: Before hand cranking the drive unit disconnect programs by pulling up all\nprogram knobs.", "page_range": "30-30", "section_hierarchy": ["2. Hand crank the machine to the lowest position (0°)."], "chunk_type": "section", "metadata": {"page_number": 30, "section_path": "2. Hand crank the machine to the lowest position (0°).", "content_lines": 1, "merged": true}, "token_count": 33, "semantic_summary": "2.  Hand crank the machine to the lowest position (0°)."}, {"chunk_id": "chunk_0130", "title": "3. Undo the coupling shaft by using a box wrench", "content": "3. Undo the coupling shaft by using a box wrench\n(13mm) and a hammer (B).\nNOTE! To undo screw clockwise! (left threaded)\n\n4. Undo the four bolts, as showed below, and lift\nthe drive unit off the washing unit.", "page_range": "30-30", "section_hierarchy": ["3. Undo the coupling shaft by using a box wrench"], "chunk_type": "section", "metadata": {"page_number": 30, "section_path": "3. Undo the coupling shaft by using a box wrench", "content_lines": 3, "merged": true}, "token_count": 55, "semantic_summary": "3.  Undo the coupling shaft by using a box wrench\n(13mm) and a hammer (B)."}, {"chunk_id": "chunk_0132", "title": "5. Protect the upper magnet from dirt (steel chips", "content": "5. Protect the upper magnet from dirt (steel chips\netc.) and damages.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "30", "section_hierarchy": ["5. Protect the upper magnet from dirt (steel chips"], "chunk_type": "section", "metadata": {"page_number": 30, "section_path": "5. Protect the upper magnet from dirt (steel chips", "content_lines": 10}, "token_count": 87, "semantic_summary": "5.  Protect the upper magnet from dirt (steel chips\netc."}, {"chunk_id": "chunk_0133", "title": "Table from Document", "content": "Table ID: table_30_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "30", "section_hierarchy": ["5. Protect the upper magnet from dirt (steel chips"], "chunk_type": "table", "metadata": {"page_number": 30, "table_id": "table_30_0", "table_rows": 8, "table_cols": 1}, "token_count": 36, "semantic_summary": "Tabular data with 8 rows"}, {"chunk_id": "chunk_0134", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 24 (36)\n# SC 90T2-CRUDE-07\nREMOVAL OF LOCATION SLEEVE/INLET HOUSE", "page_range": "31", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 31, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 3}, "token_count": 30, "semantic_summary": "Page 24 (36)\n# SC 90T2-CRUDE-07\nREMOVAL OF LOCATION SLEEVE/INLET HOUSE"}, {"chunk_id": "chunk_0135", "title": "To remove the inlet house or the location sleeve the following procedure should be followed:", "content": "To remove the inlet house or the location sleeve the following procedure should be followed:\n30082 (D).\nremounting screw (B). When remounting\nscrews B make sure that the screws hits the\ntwo grooves in the anchor pipe.\nthe deck flange with support in order to\nremove the bearings.\nremove the safety screws Pos. 148 and remove\nthe deck flange with support completely.\nNOTE! Prior to carrying out point no. 5,\nthe thread of the anchor pipe must be\nprotected by tape in order not to damage\nthe sealing.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "31", "section_hierarchy": ["Including: Jinling Shipyard", "To remove the inlet house or the location sleeve the following procedure should be followed:"], "chunk_type": "section", "metadata": {"page_number": 31, "section_path": "Including: Jinling Shipyard > To remove the inlet house or the location sleeve the following procedure should be followed:", "content_lines": 21}, "token_count": 189, "semantic_summary": "To remove the inlet house or the location sleeve the following procedure should be followed. The thread of the anchor pipe must be protected by tape in order not to damage the sealing."}, {"chunk_id": "chunk_0136", "title": "Table from Including: Jinling Shipyard > To remove the inlet house or the location sleeve the following procedure should be followed:", "content": "Table ID: table_31_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "31", "section_hierarchy": ["Including: Jinling Shipyard", "To remove the inlet house or the location sleeve the following procedure should be followed:"], "chunk_type": "table", "metadata": {"page_number": 31, "table_id": "table_31_0", "table_rows": 16, "table_cols": 1}, "token_count": 60, "semantic_summary": "Tabular data with 16 rows"}, {"chunk_id": "chunk_0137", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 25 (36)\n# SC 90T2-CRUDE-07", "page_range": "32", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 32, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 25 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0138", "title": "FAULT FINDING", "content": "FAULT FINDING\nIf the Machine does not run:\nIn order to check and get the machine in operation, carry out the following inspection:\n\n1. Inspect the strainer at the inlet flange and remove any dirt that might be stacked and use\nthe hand cranking tool to manually elevate the nozzle.", "page_range": "32-32", "section_hierarchy": ["FAULT FINDING"], "chunk_type": "section", "metadata": {"page_number": 32, "section_path": "FAULT FINDING", "content_lines": 3, "merged": true}, "token_count": 62, "semantic_summary": "FAULT FINDING\nIf the Machine does not run:\nIn order to check and get the machine in operation, carry out the following inspection:\n\n1.  Inspect the strainer at the inlet flange and remove any dirt that might be stacked and use\nthe hand cranking tool to manually elevate the nozzle."}, {"chunk_id": "chunk_0140", "title": "2. Open the inspection plug (C) and try to start the machine by opening the inlet valve", "content": "2. Open the inspection plug (C) and try to start the machine by opening the inlet valve\nslowly. Check that the pressure is correct!\n\n3. Remove the protective cap (A) at the drive unit. Check that the turbine is rotating freely\nby using an Allen key (B) as shown.", "page_range": "32-32", "section_hierarchy": ["2. Open the inspection plug (C) and try to start the machine by opening the inlet valve"], "chunk_type": "section", "metadata": {"page_number": 32, "section_path": "2. Open the inspection plug (C) and try to start the machine by opening the inlet valve", "content_lines": 2, "merged": true}, "token_count": 64, "semantic_summary": "2.  Open the inspection plug (C) and try to start the machine by opening the inlet valve\nslowly."}, {"chunk_id": "chunk_0142", "title": "4. Check through the cofferdam hole (C) if the top nut is", "content": "4. Check through the cofferdam hole (C) if the top nut is\nrotating. If so everything is OK. If not continue below.\n\n5. Remove the drive unit and check that the upper magnet is\nrunning easy by rotating it by hand. Inspect the cover if\nthere are any marks indicating that the upper magnet has\nbeen stacked on the cover.", "page_range": "32-32", "section_hierarchy": ["4. Check through the cofferdam hole (C) if the top nut is"], "chunk_type": "section", "metadata": {"page_number": 32, "section_path": "4. Check through the cofferdam hole (C) if the top nut is", "content_lines": 2, "merged": true}, "token_count": 77, "semantic_summary": "4.  Check through the cofferdam hole (C) if the top nut is\nrotating."}, {"chunk_id": "chunk_0144", "title": "6. Open up the turbine cover (Pos. 122) and inspect the", "content": "6. Open up the turbine cover (Pos. 122) and inspect the\nunderside if the lower magnet has been going in to the cover\nand created marks. If so replace the lower magnet (Pos.\n\n7. Open the turbine housing by unscrewing turbine cover (Pos. 122) with tool (30074). Use a\npair of tongs to lift it up the turbine house sleeve. Check that there is no dirt in the turbine.\nReplace the turbine and check so that it is running smoothly. Adjust the Adjusting sleeve\nif necessary.", "page_range": "32-32", "section_hierarchy": ["6. Open up the turbine cover (Pos. 122) and inspect the"], "chunk_type": "section", "metadata": {"page_number": 32, "section_path": "6. Open up the turbine cover (Pos. 122) and inspect the", "content_lines": 3, "merged": true}, "token_count": 115, "semantic_summary": "6.  Open up the turbine cover (Pos."}, {"chunk_id": "chunk_0146", "title": "8. Check the nozzle so that no dirt is stacked in it. Look at the nozzle while starting the", "content": "8. Check the nozzle so that no dirt is stacked in it. Look at the nozzle while starting the\nmachine and if the water-jet is diffuse there might be dirt stacked in the nozzle.\nIf the Machine runs with wrong speed:\n\n1. Change the turbine speed as per page 17 and check pressure and flow.\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "32-32", "section_hierarchy": ["8. Check the nozzle so that no dirt is stacked in it. Look at the nozzle while starting the"], "chunk_type": "section", "metadata": {"page_number": 32, "section_path": "8. Check the nozzle so that no dirt is stacked in it. Look at the nozzle while starting the", "content_lines": 3, "merged": true}, "token_count": 135, "semantic_summary": "8.  Check the nozzle so that no dirt is stacked in it."}, {"chunk_id": "chunk_0148", "title": "Table from Document", "content": "Table ID: table_32_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "32", "section_hierarchy": ["1. Change the turbine speed as per page 17 and check pressure and flow."], "chunk_type": "table", "metadata": {"page_number": 32, "table_id": "table_32_0", "table_rows": 9, "table_cols": 1}, "token_count": 39, "semantic_summary": "Tabular data with 9 rows"}, {"chunk_id": "chunk_0149", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 26 (36)\n# SC 90T2-CRUDE-07", "page_range": "33", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 33, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 26 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0150", "title": "TOOL KIT", "content": "TOOL KIT\nFor normal maintenance and operation the following tools are included in Scanjet tool kit:\nThis tool-kit can also be ordered as Scanjet part no. T 90T2\nPos. Qty. Part No. Description\n------------------------------------------------------------------------------------\n1 <USER> <GROUP> Tool box\n2 1 12030 Box Wrench 10 mm\n3 1 12040 Box wrench 13 mm\n4 1 12044 Box wrench 17 mm\n5 1 12051 Box wrench 25 mm\n6 1 12056 Box wrench 30 mm\n7 1 12060 Set of Hexagon bar wrench\n8 1 12061 Short 6 mm hexagon key\n9 1 12065 Tong for retaining rings\n10 2 20127 Indication arrow\n11 2 30072 Device for manual elevation\n12 1 30074 Tool for turbine cover\n14 1 30080 Tool for Inlet sleeve / Coupling sleeve\n15 1 30083 Shaft\n16 1 250007 Mobil OGL 007 grease 0,5kg\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "33", "section_hierarchy": ["TOOL KIT"], "chunk_type": "section", "metadata": {"page_number": 33, "section_path": "TOOL KIT", "content_lines": 28}, "token_count": 303, "semantic_summary": "The Scanjet tool-kit can also be ordered as Scanjet part no. T 90T2. For normal maintenance and operation the following tools are included in the tool kit:"}, {"chunk_id": "chunk_0151", "title": "Table from Document", "content": "Table ID: table_33_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n\nTable ID: table_33_1\n\n|  |\n| --- |\n|  |\n|  |", "page_range": "33-33", "section_hierarchy": ["TOOL KIT"], "chunk_type": "table", "metadata": {"page_number": 33, "table_id": "table_33_0", "table_rows": 4, "table_cols": 1, "merged": true}, "token_count": 45, "semantic_summary": "Table ID: table_33_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n\nTable ID: table_33_1\n\n|  |\n| --- |\n|  |\n|  |"}, {"chunk_id": "chunk_0153", "title": "Table from Document", "content": "Table ID: table_33_2\n\n|  |\n| --- |\n|  |\n\nTable ID: table_33_3\n\n|  |\n| --- |\n|  |", "page_range": "33-33", "section_hierarchy": ["TOOL KIT"], "chunk_type": "table", "metadata": {"page_number": 33, "table_id": "table_33_2", "table_rows": 2, "table_cols": 1, "merged": true}, "token_count": 36, "semantic_summary": "Table ID: table_33_2\n\n|  |\n| --- |\n|  |\n\nTable ID: table_33_3\n\n|  |\n| --- |\n|  |"}, {"chunk_id": "chunk_0155", "title": "Table from Document", "content": "Table ID: table_33_4\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n\nTable ID: table_33_5\n\n|  |\n| --- |\n|  |", "page_range": "33-33", "section_hierarchy": ["TOOL KIT"], "chunk_type": "table", "metadata": {"page_number": 33, "table_id": "table_33_4", "table_rows": 4, "table_cols": 1, "merged": true}, "token_count": 42, "semantic_summary": "Table ID: table_33_4\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n\nTable ID: table_33_5\n\n|  |\n| --- |\n|  |"}, {"chunk_id": "chunk_0157", "title": "Table from Document", "content": "Table ID: table_33_6\n\n|  |\n| --- |\n|  |", "page_range": "33", "section_hierarchy": ["TOOL KIT"], "chunk_type": "table", "metadata": {"page_number": 33, "table_id": "table_33_6", "table_rows": 2, "table_cols": 1}, "token_count": 18, "semantic_summary": "Tabular data with 2 rows"}, {"chunk_id": "chunk_0158", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 27 (36)\n# SC 90T2-CRUDE-07\nSpare parts kit SC 90T2 /Vessel. List dated 2008-07-10\nThis spare part kit can also be ordered as Scanjet part No. S 90T2\nPOS. NO QTY. SC PART NO. DESCRIPTION Material\n4 2 120319 Ring SS\n11 3 104441 Screw SS\n12 3 106104 Washer SS\n40 2 104758 Screw SS\n76 2 120262 O-Ring Viton\n100 2 120305 Retaining Ring Bronze\n101 2 109239 O-Ring Viton\n105 2 109264 O-Ring Viton\n110 2 109268 O-Ring Viton\n112 2 90270 Plug Plastic\n142 2 109263 O-Ring Viton\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "34", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 34, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 24}, "token_count": 260, "semantic_summary": "This spare part kit can also be ordered as Scanjet part No. S 90T2. Page 27 (36)                # SC 90t2-CRUDE-07                Spare parts kit SC 90 t2 /Vessel. List dated 2008-07-10                ."}, {"chunk_id": "chunk_0159", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_34_0\n\n| Spare parts kit SC 90T2 /Vessel. List dated 2008-07-10\nThis spare part kit can also be ordered as Scanjet part No. S 90T2 |  |  |  |  |\n| --- | --- | --- | --- | --- |\n| POS. NO | QTY. | SC PART NO. | DESCRIPTION | Material |\n| 4 | 2 | 120319 | Ring | SS |\n| 11 | 3 | 104441 | Screw | SS |\n| 12 | 3 | 106104 | Washer | SS |\n| 40 | 2 | 104758 | Screw | SS |\n| 76 | 2 | 120262 | O-Ring | Viton |\n| 100 | 2 | 120305 | Retaining Ring | Bronze |\n| 101 | 2 | 109239 | O-Ring | Viton |\n| 105 | 2 | 109264 | O-Ring | Viton |\n| 110 | 2 | 109268 | O-Ring | Viton |\n| 112 | 2 | 90270 | Plug | Plastic |\n| 142 | 2 | 109263 | O-Ring | Viton |\n|  |  |  |  |  |", "page_range": "34", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 34, "table_id": "table_34_0", "table_rows": 14, "table_cols": 5}, "token_count": 282, "semantic_summary": "Tabular data with 14 rows"}, {"chunk_id": "chunk_0160", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 28 (36)\n# SC 90T2-CRUDE-07\nService kits\nTank cleaning machines are installed and operated in extremely harsh marine\nconditions. In order to ensure continued safe operation of your Scanjet tank\ncleaning machines it is advised to follow given service instructions.\nScanjet have thoroughly identified the components which during regular\nintervals should be checked, and if necessary replaced. In order to prevent\nseawater penetrations or similar, assuring machines safe, smooth and trouble\nfree operation. Those components are set together in various service kits.\nNaturally perfectly dedicated and optimized for any Scanjet model and type of\nmachine.\nService intervals are described in the maintenance section of this manual.\nService kits are rapidly available and easy to order, as well as being more\neconomical compared to ordering of parts individually.\nScanjet spare part No. Description\nKIT 90T2 OGU Complete O-Ring kit for SC90T2 Washing Unit\nKIT 90T2 WGU Complete Wear kit for SC90T2 Washing Unit\nKIT 30T/90T2 ODU Complete O-Ring kit for SC290 Drive Unit\nS 90T2 Scanjet basic spare part kit\nT 90T2 Scanjet basic tool kit including all necessary tools to\nservice the machine\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "35", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 35, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 30}, "token_count": 341, "semantic_summary": "Service kits are rapidly available and easy to order. Service intervals are described in the maintenance section of this manual. Service kits are available for any Scanjet model and type of machine."}, {"chunk_id": "chunk_0161", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 29 (36)\n# SC 90T2-CRUDE-07\nHow to order spare parts\nWhen ordering spare parts the following data must be referred to for securing a correct and\nrapid delivery.\nPlease note that each washing unit is marked at the inlet housing as showed on fig below.\nName of Vessel / Hull no: Name and original new build no.", "page_range": "36", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 36, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 7}, "token_count": 77, "semantic_summary": "Page 29 (36)\n# SC 90T2-CRUDE-07\nHow to order spare parts\nWhen ordering spare parts the following data must be referred to for securing a correct and\nrapid delivery. \nPlease note that each washing unit is marked at the inlet housing as showed on fig below."}, {"chunk_id": "chunk_0162", "title": "Invoice address: Customer name and address", "content": "Invoice address: Customer name and address\n\nConsignee: Customer responsible person", "page_range": "36-36", "section_hierarchy": ["Including: Jinling Shipyard", "Invoice address: Customer name and address"], "chunk_type": "section", "metadata": {"page_number": 36, "section_path": "Including: Jinling Shipyard > Invoice address: Customer name and address", "content_lines": 1, "merged": true}, "token_count": 15, "semantic_summary": "Invoice address: Customer name and address\n\nConsignee: Customer responsible person"}, {"chunk_id": "chunk_0164", "title": "Your order no:", "content": "Your order no:\nContact Person: Customer contact person\n\nMode of delivery: By mail, courier etc.\nLatest ETA destination: Shipping address", "page_range": "36-36", "section_hierarchy": ["Including: Jinling Shipyard", "Your order no:"], "chunk_type": "section", "metadata": {"page_number": 36, "section_path": "Including: Jinling Shipyard > Your order no:", "content_lines": 2, "merged": true}, "token_count": 27, "semantic_summary": "Your order no:\nContact Person: Customer contact person\n\nMode of delivery: By mail, courier etc. \nLatest ETA destination: Shipping address."}, {"chunk_id": "chunk_0166", "title": "Shipping mark: Marking of delivery", "content": "Shipping mark: Marking of delivery\n\nType of equipment: SC 90T2, nozzle size", "page_range": "36-36", "section_hierarchy": ["Including: Jinling Shipyard", "Shipping mark: Marking of delivery"], "chunk_type": "section", "metadata": {"page_number": 36, "section_path": "Including: Jinling Shipyard > Shipping mark: Marking of delivery", "content_lines": 1, "merged": true}, "token_count": 20, "semantic_summary": "Shipping mark: Marking of delivery\n\nType of equipment: SC 90T2, nozzle size"}, {"chunk_id": "chunk_0168", "title": "Serial no: Serial no. on machines", "content": "Serial no: Serial no. on machines\n\nSpare part list:", "page_range": "36-36", "section_hierarchy": ["Including: Jinling Shipyard", "Serial no: Serial no. on machines"], "chunk_type": "section", "metadata": {"page_number": 36, "section_path": "Including: Jinling Shipyard > Serial no: Serial no. on machines", "content_lines": 1, "merged": true}, "token_count": 14, "semantic_summary": "Serial no: Serial no. on machines\n\nSpare part list:"}, {"chunk_id": "chunk_0170", "title": "POS PART NO QTY DESCRIPTION", "content": "POS PART NO QTY DESCRIPTION\n_______________________________________________________\n....... ................. ....... ...................................\n....... ................. ....... ...................................\n....... ................. ....... ...................................\n....... ................. ....... ...................................\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "36", "section_hierarchy": ["POS PART NO QTY DESCRIPTION"], "chunk_type": "section", "metadata": {"page_number": 36, "section_path": "POS PART NO QTY DESCRIPTION", "content_lines": 14}, "token_count": 110, "semantic_summary": "Scanjet Marine AB ABSödra Långebergsgatan 36, Göteborg, Sweden. P.O. Box 9316, 9316 SE-400 97 Götborg. Phone: +46 31 338 7530. Email: <EMAIL>. Web: www.scanJet.se"}, {"chunk_id": "chunk_0171", "title": "Table from Document", "content": "Table ID: table_36_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "36", "section_hierarchy": ["POS PART NO QTY DESCRIPTION"], "chunk_type": "table", "metadata": {"page_number": 36, "table_id": "table_36_0", "table_rows": 10, "table_cols": 1}, "token_count": 42, "semantic_summary": "Tabular data with 10 rows"}, {"chunk_id": "chunk_0172", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 30 (36)\n# SC 90T2-CRUDE-07\nSpare part breakdown Driving unit SC290. List dated 2008-07-10\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "37", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 37, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 11}, "token_count": 107, "semantic_summary": "Page 30 (36)\n# SC 90T2-CRUDE-07\nSpare part breakdown Driving unit SC290.  List dated 2008-07-10\nScanjet Marine AB\nSödra Långebergsgatan 36\nP."}, {"chunk_id": "chunk_0173", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_37_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "37", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 37, "table_id": "table_37_0", "table_rows": 16, "table_cols": 1}, "token_count": 60, "semantic_summary": "Tabular data with 16 rows"}, {"chunk_id": "chunk_0174", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 31 (36)\n# SC 90T2-CRUDE-07\nSpare part breakdown Washing unit SC 90T2. List dated 2008-07-10\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "38", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 38, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 11}, "token_count": 110, "semantic_summary": "Page 31 (36)\n# SC 90T2-CRUDE-07\nSpare part breakdown Washing unit SC 90T2.  List dated 2008-07-10\nScanjet Marine AB\nSödra Långebergsgatan 36\nP."}, {"chunk_id": "chunk_0175", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_38_0\n\n|  |\n| --- |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |\n|  |", "page_range": "38", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 38, "table_id": "table_38_0", "table_rows": 31, "table_cols": 1}, "token_count": 105, "semantic_summary": "Tabular data with 31 rows"}, {"chunk_id": "chunk_0176", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 32 (36)\n# SC 90T2-CRUDE-07\nSpare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”.\nPOS. NO QTY. SC PART NO. DESCRIPTION\n1 1 30064 Shaft\n2 1 30065 Washer\n3 1 30066 Cover\n4 1 120319 Ring\n5 1 104443 Screw\n6 3 30063-1 Knob 1,5º\n6 (3) 30063-3 Knob 2,5º, According to order\n7 3 105089 Screw\n8 3 30062 Program shaft\n9 1 109035 O-Ring\n10 3 108730 O-Ring\n11 8 104441 <PERSON>rew\n12 9 106104 Washer\n13 1 30003-1111 Cover 1,5º Assembly with O-rings, shafts and bearings", "page_range": "39", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 39, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 20}, "token_count": 233, "semantic_summary": "Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10. Spare parts number may be changed without prior notice. Final spare parts numbers will be issued for ‘shipset manual’"}, {"chunk_id": "chunk_0177", "title": "13 Optional 30003-1121 Cover 2,5º Assembly with O-rings, shafts and bearings", "content": "13 Optional 30003-1121 Cover 2,5º Assembly with O-rings, shafts and bearings\n14 1 30038 Bearing\n15 4 104862 Screw\n16 1 30049 Gear\n17 1 120258 O-Ring\n18 1 20083 Guide sleeve\n19 1 30051 Driving sleeve, Incl. Pos 17,18\n20 1 103261 Retaining ring\n21 1 20089 Ruler\n22 1 30067 Sealing\n24 1 25052 Indicator\n25 1 120318 Nut\n26 1 120259 O-Ring\n27 1 120348 Plug\n28 1 30059 Coupling shaft\n29 1 20062-2 Washer\n30 1 30036 Washer\n31 6 104741 <PERSON>rew\n32 1 30052 Gear, Incl. Pos 52, 53, 54\n33 1 30034 Sleeve\n34 1 30026 <PERSON><PERSON>\n35 1 109211 O-Ring\n36 1 20064 Continous screw, Incl. Pos 35, 37, 38\n37 1 108765 O-Ring\n38 1 20085 Washer\n39 1 108806 O-Ring\n40 4 104758 <PERSON>rew\n41 1 30069 Pin\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "39", "section_hierarchy": ["13 Optional 30003-1121 Cover 2,5º Assembly with O-rings, shafts and bearings"], "chunk_type": "section", "metadata": {"page_number": 39, "section_path": "13 Optional 30003-1121 Cover 2,5º Assembly with O-rings, shafts and bearings", "content_lines": 36}, "token_count": 368, "semantic_summary": "13 Optional 30003-1121 Cover 2,5º Assembly with O-rings, shafts and bearings. 1 30038 Bearing. 15 4 104862 Screw. 16 1 30049 Gear. 17 1 120258 O-Ring18 1 20083 Guide sleeve."}, {"chunk_id": "chunk_0178", "title": "Table from Document", "content": "Table ID: table_39_0\n\n| Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”. |  |  |  |\n| --- | --- | --- | --- |\n| POS. NO | QTY. | SC PART NO. | DESCRIPTION |\n| 1 | 1 | 30064 | Shaft |\n| 2 | 1 | 30065 | Washer |\n| 3 | 1 | 30066 | Cover |\n| 4 | 1 | 120319 | Ring |\n| 5 | 1 | 104443 | Screw |\n| 6 | 3 | 30063-1 | Knob 1,5º |\n| 6 | (3) | 30063-3 | Knob 2,5º, According to order |\n| 7 | 3 | 105089 | Screw |\n| 8 | 3 | 30062 | Program shaft |\n| 9 | 1 | 109035 | O-Ring |\n| 10 | 3 | 108730 | O-Ring |\n| 11 | 8 | 104441 | Screw |\n| 12 | 9 | 106104 | Washer |\n| 13 | 1 | 30003-1111 | Cover 1,5º Assembly with O-rings, shafts and bearings |\n| 13 | Optional | 30003-1121 | Cover 2,5º Assembly with O-rings, shafts and bearings |\n| 14 | 1 | 30038 | Bearing |\n| 15 | 4 | 104862 | Screw |\n| 16 | 1 | 30049 | Gear |\n| 17 | 1 | 120258 | O-Ring |\n| 18 | 1 | 20083 | Guide sleeve |\n| 19 | 1 | 30051 | Driving sleeve, Incl. Pos 17,18 |\n| 20 | 1 | 103261 | Retaining ring |\n| 21 | 1 | 20089 | Ruler |\n| 22 | 1 | 30067 | Sealing |\n| 24 | 1 | 25052 | Indicator |\n| 25 | 1 | 120318 | Nut |\n| 26 | 1 | 120259 | O-Ring |\n| 27 | 1 | 120348 | Plug |\n| 28 | 1 | 30059 | Coupling shaft |\n| 29 | 1 | 20062-2 | Washer |\n| 30 | 1 | 30036 | Washer |\n| 31 | 6 | 104741 | Screw |\n| 32 | 1 | 30052 | Gear, Incl. Pos 52, 53, 54 |\n| 33 | 1 | 30034 | Sleeve |\n| 34 | 1 | 30026 | Washer |\n| 35 | 1 | 109211 | O-Ring |\n| 36 | 1 | 20064 | Continous screw, Incl. Pos 35, 37, 38 |\n| 37 | 1 | 108765 | O-Ring |\n| 38 | 1 | 20085 | Washer |\n| 39 | 1 | 108806 | O-Ring |\n| 40 | 4 | 104758 | Screw |\n| 41 | 1 | 30069 | Pin |", "page_range": "39", "section_hierarchy": ["13 Optional 30003-1121 Cover 2,5º Assembly with O-rings, shafts and bearings"], "chunk_type": "table", "metadata": {"page_number": 39, "table_id": "table_39_0", "table_rows": 44, "table_cols": 4}, "token_count": 752, "semantic_summary": "Tabular data with 44 rows"}, {"chunk_id": "chunk_0179", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 33 (36)\n# SC 90T2-CRUDE-07\nSpare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”.\n42 1 25076 Spring\n43 4 106210 Retaining Ring\n43a 1 120319 New type replaces one of 43\n44 3 30068 Washer\n44a 1 30088 New type replaces one of 44\n45 1 30053-1 Feeder Arm 1,5/3/4,5, Incl. Po<PERSON> 46, according to order\n45 1 30053-2 Feeder Arm 2,5/5/7,5, Incl. Po<PERSON> 46, according to order\n46 1 108205 Freewheel, Order Pos. 45\n47 1 120288 Washer\n48 1 30045 Gear\n49 1 108205 Freewheel, Order Pos. 51\n50 1 30073 Pin\n51 1 30054 Bearing House, Incl. Pos. 49\n52 1 20091 Key\n53 1 102470 Rivet\n54 1 120312 Bearing\n55 5 120342 Bearing\n56 1 30044 Shaft\n57 1 103615 Key\n58 1 30048 Gear\n59 1 30028 Washer\n60 1 120343 Bearing\n61 3 120286 Washer\n62 3 106208 Retaining Ring\n63 2 30047 Gear\n64 1 30043 Shaft\n65 3 103614 Key\n66 3 30027 Bearing\n67 3 120341 <PERSON><PERSON>\n68 2 104641 Screw\n70 1 30055 Plate, Incl. Pos. 55 (5 pcs.)\n71 1 30042 Shaft\n72 1 30046 Gear\n73 1 30041 Shaft\n74 1 30001-11 Housing, Incl. Pos. 60, 67 (3 pcs.)\n75 1 120311 Plug\n76 1 120262 O-Ring\n77 1 109042 O-Ring\n78 1 30040 Shaft\n79 1 106927 Ball Bearing\n80 1 106260 Retaining Ring\n81 1 20047 Upper magnet\n82 1 105964 Nut\n83 2 120326 Guide Bearing\n84 3 120347 Ring\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "40", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 40, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 58}, "token_count": 626, "semantic_summary": "Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10. Spare parts number may be changed without prior notice. Final spare parts numbers will be issued for “shipset manual’."}, {"chunk_id": "chunk_0180", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_40_0\n\n| Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”. |  |  |  |\n| --- | --- | --- | --- |\n| 42 | 1 | 25076 | Spring |\n| 43 | 4 | 106210 | Retaining Ring |\n| 43a | 1 | 120319 | New type replaces one of 43 |\n| 44 | 3 | 30068 | Washer |\n| 44a | 1 | 30088 | New type replaces one of 44 |\n| 45 | 1 | 30053-1 | Feeder Arm 1,5/3/4,5, Incl. Pos 46, according to order |\n| 45 | 1 | 30053-2 | Feeder Arm 2,5/5/7,5, Incl. Pos 46, according to order |\n| 46 | 1 | 108205 | Freewheel, Order Pos. 45 |\n| 47 | 1 | 120288 | Washer |\n| 48 | 1 | 30045 | Gear |\n| 49 | 1 | 108205 | Freewheel, Order Pos. 51 |\n| 50 | 1 | 30073 | Pin |\n| 51 | 1 | 30054 | Bearing House, Incl. Pos. 49 |\n| 52 | 1 | 20091 | Key |\n| 53 | 1 | 102470 | Rivet |\n| 54 | 1 | 120312 | Bearing |\n| 55 | 5 | 120342 | Bearing |\n| 56 | 1 | 30044 | Shaft |\n| 57 | 1 | 103615 | Key |\n| 58 | 1 | 30048 | Gear |\n| 59 | 1 | 30028 | Washer |\n| 60 | 1 | 120343 | Bearing |\n| 61 | 3 | 120286 | Washer |\n| 62 | 3 | 106208 | Retaining Ring |\n| 63 | 2 | 30047 | Gear |\n| 64 | 1 | 30043 | Shaft |\n| 65 | 3 | 103614 | Key |\n| 66 | 3 | 30027 | Bearing |\n| 67 | 3 | 120341 | Bearing |\n| 68 | 2 | 104641 | Screw |\n| 70 | 1 | 30055 | Plate, Incl. Pos. 55 (5 pcs.) |\n| 71 | 1 | 30042 | Shaft |\n| 72 | 1 | 30046 | Gear |\n| 73 | 1 | 30041 | Shaft |\n| 74 | 1 | 30001-11 | Housing, Incl. Pos. 60, 67 (3 pcs.) |\n| 75 | 1 | 120311 | Plug |\n| 76 | 1 | 120262 | O-Ring |\n| 77 | 1 | 109042 | O-Ring |\n| 78 | 1 | 30040 | Shaft |\n| 79 | 1 | 106927 | Ball Bearing |\n| 80 | 1 | 106260 | Retaining Ring |\n| 81 | 1 | 20047 | Upper magnet |\n| 82 | 1 | 105964 | Nut |\n| 83 | 2 | 120326 | Guide Bearing |\n| 84 | 3 | 120347 | Ring |", "page_range": "40", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 40, "table_id": "table_40_0", "table_rows": 46, "table_cols": 4}, "token_count": 789, "semantic_summary": "Tabular data with 46 rows"}, {"chunk_id": "chunk_0181", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 34 (36)\n# SC 90T2-CRUDE-07\nSpare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”.\n85 1 120279 Washer\n86 3 30127-1 Spring Washer\n87 1 120306 Drainage plug\n88 1 30128 Sealing washer\n100 1 120305 Retaining Ring\n101 1 109239 O-Ring\n102 2 104737 Screw\n103 1 90317 Topnut Incl. Pos. 101, 102, 105\n104 2 105104 <PERSON>rew\n105 1 109264 O-Ring\n106 1 30021 Bearing\n107 1 30023 Bearing\n108 1 30009 Sleeve, Including 109, 110\n109 1 109275 O-Ring\n110 1 109268 O-Ring\n112 1 90270 Plug\n113 4 106509 Rivet\n114 1 70104 Plate\n115 4 102550 Nut\n116 4 106151 Washer\n117 1 105096 Screw\n118 1 109268 O-Ring “Old PTFE O-ring replaced by Viton”\n119 1 90318 Anchor pipe\n120 1 90330 Bearing\n121 1 90335 Sealing\n122 1 30008 Cover, Including 123, 124\n123 1 120260 O-Ring\n124 1 Bearing (Order 122)\n125 1 120295 Ball\n126 1 20006 Lower magnet, Including 127\n127 1 20029 Shaft\n128 1 20104 Spring\n129 1 20017 Bearing\n130 1 90329 Turbine housing\n131 1 104206 Pin\n132 1 90346 Turbine shaft, Including 131 and ball\n133 1 20013-44 Turbine T1 (Nozzle Ø25-Ø34)\n133 a 21013-44 Turbine T2 (Nozzle Ø20-Ø24)\n134 2 106576 Split pin\n135 1 30319-40 Flow washer\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "41", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 41, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 53}, "token_count": 566, "semantic_summary": "Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10. Spare parts number may be changed without prior notice. Final spare parts numbers will be issued for “shipset manual’."}, {"chunk_id": "chunk_0182", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_41_0\n\n| Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”. |  |  |  |\n| --- | --- | --- | --- |\n| 85 | 1 | 120279 | Washer |\n| 86 | 3 | 30127-1 | Spring Washer |\n| 87 | 1 | 120306 | Drainage plug |\n| 88 | 1 | 30128 | Sealing washer |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n| 100 | 1 | 120305 | Retaining Ring |\n| 101 | 1 | 109239 | O-Ring |\n| 102 | 2 | 104737 | Screw |\n| 103 | 1 | 90317 | Topnut Incl. Pos. 101, 102, 105 |\n| 104 | 2 | 105104 | Screw |\n| 105 | 1 | 109264 | O-Ring |\n| 106 | 1 | 30021 | Bearing |\n| 107 | 1 | 30023 | Bearing |\n| 108 | 1 | 30009 | Sleeve, Including 109, 110 |\n| 109 | 1 | 109275 | O-Ring |\n| 110 | 1 | 109268 | O-Ring |\n| 111 | 1 | 90380-xx | Inlet house (xx according to order see page 7) |\n| 112 | 1 | 90270 | Plug |\n| 113 | 4 | 106509 | Rivet |\n| 114 | 1 | 70104 | Plate |\n| 115 | 4 | 102550 | Nut |\n| 116 | 4 | 106151 | <PERSON>her |\n| 117 | 1 | 105096 | Screw |\n| 118 | 1 | 109268 | O-Ring “Old PTFE O-ring replaced by Viton” |\n| 119 | 1 | 90318 | Anchor pipe |\n| 120 | 1 | 90330 | Bearing |\n| 121 | 1 | 90335 | Sealing |\n| 122 | 1 | 30008 | Cover, Including 123, 124 |\n| 123 | 1 | 120260 | O-Ring |\n| 124 | 1 |  | Bearing (Order 122) |\n| 125 | 1 | 120295 | Ball |\n| 126 | 1 | 20006 | Lower magnet, Including 127 |\n| 127 | 1 | 20029 | Shaft |\n| 128 | 1 | 20104 | Spring |\n| 129 | 1 | 20017 | Bearing |\n| 130 | 1 | 90329 | Turbine housing |\n| 131 | 1 | 104206 | Pin |\n| 132 | 1 | 90346 | Turbine shaft, Including 131 and ball |\n| 133 | 1 | 20013-44 | Turbine T1 (Nozzle Ø25-Ø34) |\n| 133 a |  | 21013-44 | Turbine T2 (Nozzle Ø20-Ø24) |\n| 134 | 2 | 106576 | Split pin |\n| 135 | 1 | 30319-40 | Flow washer |", "page_range": "41", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 41, "table_id": "table_41_0", "table_rows": 46, "table_cols": 4}, "token_count": 771, "semantic_summary": "Tabular data with 46 rows"}, {"chunk_id": "chunk_0183", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 35 (36)\n# SC 90T2-CRUDE-07\nSpare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”.\n136 1 90328-5 Turbine sleeve\n137 1 25107 Strainer\n138 1 25108 Ring\n139 1 Bearing, Included in 141\n140 1 Bearing, Included in 141\n141 1 20005-2 Adjusting sleeve, Including 139 and 140\n142 1 109263 O-Ring\n143 1 20026 Nut\n144 1 20025 Cup\n145 1 109093 O-Ring\n146 4 120490 Pin bolt\n148 2 104457 Screw\n149 2 109329 O-Ring\n150 1 90332 Bearing\n151 1 90337-xxx Lifting rod\n152 2 90338 Pin\n153 4 90340 Distance\n154 2 90339 Pin\n155 4 90341 Distance\n156 4 90334 Pin\n157 1 90361-xxx Main pipe (according to order xxx is length)\n158 1 105096 Screw\n159 1 106589 Split pin\n160 1 90331 Bearing flange\n161 2 90016 Bearing\n162 1 90324 Bottom housing\n163 1 50063 Bottom plug\n164 1 50064 Washer\n165 1 50066 Retaining Ring\n166 1 90342 Nozzle housing (including gear and pin) >24mm\n(1) (90342-2) Nozzle housing (including gear and pin) <26mm\n167 2 105095 Screw\n168 1 90035 Flow pipe\n169 1 90034-xx Nozzle (xx is outlet diameter)\n170 1 90368 Washer\n171 1 90370 Ring\n172 1 30020 Bearing\nOptional high performance nozzle type\n173 2 90158 Flow Guide\n174 1 90156 Flow Pipe\n175 1 90155-xx Nozzle (xx is outlet diameter)\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "42", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 42, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 54}, "token_count": 554, "semantic_summary": "Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10. Spare parts number may be changed without prior notice. Final spare parts numbers will be issued for “shipset manual’."}, {"chunk_id": "chunk_0184", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_42_0\n\n| Spare parts breakdown SCANJET SC 90 T2 List dated 2008-07-10\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”. |  |  |  |\n| --- | --- | --- | --- |\n| 136 | 1 | 90328-5 | Turbine sleeve |\n| 137 | 1 | 25107 | Strainer |\n| 138 | 1 | 25108 | Ring |\n| 139 | 1 |  | Bearing, Included in 141 |\n| 140 | 1 |  | Bearing, Included in 141 |\n| 141 | 1 | 20005-2 | Adjusting sleeve, Including 139 and 140 |\n| 142 | 1 | 109263 | O-Ring |\n| 143 | 1 | 20026 | Nut |\n| 144 | 1 | 20025 | Cup |\n| 145 | 1 | 109093 | O-Ring |\n| 146 | 4 | 120490 | Pin bolt |\n| 147 | 1 | 90378-xx | Deckflange with support (according to order see page 7) |\n| 148 | 2 | 104457 | Screw |\n| 149 | 2 | 109329 | O-Ring |\n| 150 | 1 | 90332 | Bearing |\n| 151 | 1 | 90337-xxx | Lifting rod |\n| 152 | 2 | 90338 | Pin |\n| 153 | 4 | 90340 | Distance |\n| 154 | 2 | 90339 | Pin |\n| 155 | 4 | 90341 | Distance |\n| 156 | 4 | 90334 | Pin |\n| 157 | 1 | 90361-xxx | Main pipe (according to order xxx is length) |\n| 158 | 1 | 105096 | Screw |\n| 159 | 1 | 106589 | Split pin |\n| 160 | 1 | 90331 | Bearing flange |\n| 161 | 2 | 90016 | Bearing |\n| 162 | 1 | 90324 | Bottom housing |\n| 163 | 1 | 50063 | Bottom plug |\n| 164 | 1 | 50064 | Washer |\n| 165 | 1 | 50066 | Retaining Ring |\n| 166 | 1 | 90342 | Nozzle housing (including gear and pin) >24mm |\n|  | (1) | (90342-2) | Nozzle housing (including gear and pin) <26mm |\n| 167 | 2 | 105095 | Screw |\n| 168 | 1 | 90035 | Flow pipe |\n| 169 | 1 | 90034-xx | Nozzle (xx is outlet diameter) |\n| 170 | 1 | 90368 | Washer |\n| 171 | 1 | 90370 | Ring |\n| 172 | 1 | 30020 | Bearing |\n| Optional high performance nozzle type |  |  |  |\n| 173 | 2 | 90158 | Flow Guide |\n| 174 | 1 | 90156 | Flow Pipe |\n| 175 | 1 | 90155-xx | Nozzle (xx is outlet diameter) |", "page_range": "42", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 42, "table_id": "table_42_0", "table_rows": 43, "table_cols": 4}, "token_count": 730, "semantic_summary": "Tabular data with 43 rows"}, {"chunk_id": "chunk_0185", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 36 (36)\n# SC 90T2-CRUDE-07", "page_range": "43", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 43, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 17, "semantic_summary": "Page 36 (36)\n# SC 90T2-CRUDE-07"}, {"chunk_id": "chunk_0186", "title": "NOTES", "content": "NOTES\nPOS. NO SC PART NO. Remarks\nScanjet Marine AB\nSödra Långebergsgatan 36\nP.O. Box 9316\nSE-400 97 Göteborg, Sweden\nTelephone +46 31 338 7530\nTelefax +46 31 338 7540\nEmail <EMAIL>\nWeb www.scanjet.se", "page_range": "43", "section_hierarchy": ["NOTES"], "chunk_type": "section", "metadata": {"page_number": 43, "section_path": "NOTES", "content_lines": 10}, "token_count": 82, "semantic_summary": "NOTES\nPOS.  NO SC PART NO."}, {"chunk_id": "chunk_0187", "title": "Table from Document", "content": "Table ID: table_43_0\n\n| NOTES |  |  |\n| --- | --- | --- |\n| POS. NO | SC PART NO. | Remarks |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |", "page_range": "43", "section_hierarchy": ["NOTES"], "chunk_type": "table", "metadata": {"page_number": 43, "table_id": "table_43_0", "table_rows": 24, "table_cols": 3}, "token_count": 189, "semantic_summary": "Tabular data with 24 rows"}, {"chunk_id": "chunk_0188", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 1 (5)\n# SC 90T2-RT-01\nSupplemental", "page_range": "44", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 44, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 3}, "token_count": 20, "semantic_summary": "Page 1 (5)\n# SC 90T2-RT-01\nSupplemental"}, {"chunk_id": "chunk_0189", "title": "INSTRUCTION", "content": "INSTRUCTION\n\nMANUAL\nSC90T2-RT-bilaga-040319.doc\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0)416 511 656, Telephone no. +46 (0)416 513 100", "page_range": "44-44", "section_hierarchy": ["INSTRUCTION"], "chunk_type": "section", "metadata": {"page_number": 44, "section_path": "INSTRUCTION", "content_lines": 1, "merged": true}, "token_count": 73, "semantic_summary": "INSTRUCTION\n\nMANUAL\nSC90T2-RT-bilaga-040319. doc\nSCANJET MARINE AB, P."}, {"chunk_id": "chunk_0191", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 2 (5)\n# SC 90T2-RT-01", "page_range": "45", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 45, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 16, "semantic_summary": "Page 2 (5)\n# SC 90T2-RT-01"}, {"chunk_id": "chunk_0192", "title": "INTRODUCTION", "content": "INTRODUCTION\nThe SC90T2 RT is a specially developed machine for cleaning of bulk carriers. It is based on\na standard SC90T2 and fitted with a special flange system to allow retraction of the machine.\nThis allows the machine to be lifted out of reach from cargo, that could damage it. The\nlowered down and secured with bolts to the deck. And when it is retracted it is lifted up and\nsecured by the main pipe with bolts.\n\nHANDLING\nResting position\nResting stand\nHow to get the machine into cleaning position? Not supplied by\nScanjet Marine AB", "page_range": "45-45", "section_hierarchy": ["INTRODUCTION"], "chunk_type": "section", "metadata": {"page_number": 45, "section_path": "INTRODUCTION", "content_lines": 6, "merged": true}, "token_count": 126, "semantic_summary": "The SC90T2 RT is a specially developed machine for cleaning of bulk carriers. It is fitted with a special flange system to allow retraction of the machine. This allows the machine to be lifted out of reach from cargo."}, {"chunk_id": "chunk_0194", "title": "1. Put a sling around the machine", "content": "1. Put a sling around the machine\n\n2. Remove the bolts at the inlet flange to\nloosen from the resting stand", "page_range": "45-45", "section_hierarchy": ["1. Put a sling around the machine"], "chunk_type": "section", "metadata": {"page_number": 45, "section_path": "1. Put a sling around the machine", "content_lines": 1, "merged": true}, "token_count": 27, "semantic_summary": "1.  Put a sling around the machine\n\n2."}, {"chunk_id": "chunk_0196", "title": "3. Remove the for nuts at the deck flange", "content": "3. Remove the for nuts at the deck flange\n\n4. Lower the machine carefully down and fit\non the eight bolts and tighten the nuts\nNote! Watch your\nfingers when handling\nthe machine to avoid\ninjury.\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0)416 511 656, Telephone no. +46 (0)416 513 100", "page_range": "45-45", "section_hierarchy": ["3. Remove the for nuts at the deck flange"], "chunk_type": "section", "metadata": {"page_number": 45, "section_path": "3. Remove the for nuts at the deck flange", "content_lines": 1, "merged": true}, "token_count": 102, "semantic_summary": "3.  Remove the for nuts at the deck flange\n\n4."}, {"chunk_id": "chunk_0198", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 3 (5)\n# SC 90T2-RT-01\nHow to get the machine into resting position?", "page_range": "46", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 46, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 3}, "token_count": 26, "semantic_summary": "Page 3 (5)\n# SC 90T2-RT-01\nHow to get the machine into resting position?"}, {"chunk_id": "chunk_0199", "title": "1. When the cleaning is finalized you should get the machine into resting position", "content": "1. When the cleaning is finalized you should get the machine into resting position\n\n2. Loosen the eight bolts at the inlet flange. Put a sling around the machine. Loosen the\neight nuts at the deck flange. Lift up the machine carefully. Fit the four bolts in the\nholes and tighten the nuts.", "page_range": "46-46", "section_hierarchy": ["1. When the cleaning is finalized you should get the machine into resting position"], "chunk_type": "section", "metadata": {"page_number": 46, "section_path": "1. When the cleaning is finalized you should get the machine into resting position", "content_lines": 1, "merged": true}, "token_count": 67, "semantic_summary": "1.  When the cleaning is finalized you should get the machine into resting position\n\n2."}, {"chunk_id": "chunk_0201", "title": "3. Secure the inlet flange to the resting stand with at least four bolts. It might be", "content": "3. Secure the inlet flange to the resting stand with at least four bolts. It might be\nnecessary to rotate the parking flange in order to fit with the bolts at the resting stand.\nNote! Watch your\nfingers when handling\nthe machine to avoid\nCleaning position\ninjury.\nRotating flange\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0)416 511 656, Telephone no. +46 (0)416 513 100", "page_range": "46", "section_hierarchy": ["3. Secure the inlet flange to the resting stand with at least four bolts. It might be"], "chunk_type": "section", "metadata": {"page_number": 46, "section_path": "3. Secure the inlet flange to the resting stand with at least four bolts. It might be", "content_lines": 10}, "token_count": 119, "semantic_summary": "3.  Secure the inlet flange to the resting stand with at least four bolts."}, {"chunk_id": "chunk_0202", "title": "Table from Document", "content": "Table ID: table_46_0\n\n| Cleaning | position |\n| --- | --- |", "page_range": "46", "section_hierarchy": ["3. Secure the inlet flange to the resting stand with at least four bolts. It might be"], "chunk_type": "table", "metadata": {"page_number": 46, "table_id": "table_46_0", "table_rows": 1, "table_cols": 2}, "token_count": 19, "semantic_summary": "Tabular data with 1 rows"}, {"chunk_id": "chunk_0203", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 4 (5)\n# SC 90T2-RT-01\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0)416 511 656, Telephone no. +46 (0)416 513 100\n\nPage 5 (5)\n# SC 90T2-RT-01\nSpare parts breakdown pipe and flanges SCANJET SC 90 T2 RT List dated 2004-03-19\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”.\nPOS. NO QTY. SC PART NO. DESCRIPTION\n1 1 90366-xxx Pipe\n2 12 102552 Nut\n3 12 102639 Washer\n4 1 90090 Sealing\n5 8 120274 Pin bolt\n6 1 90082-xx Flange acording to order see below\n90082-10 Ø480 / Ø435 No of holes 12\n90082-20 Ø420 / Ø380 No of holes 8\n90082-21 Ø430 / Ø390 No of holes 12\n90082-22 Ø480 / Ø435 No of holes 8\n90082-23 Ø540 / Ø495 No of holes 16\n7 1 109592 O-Ring\n8 1 90086-2 Sealing\n9 4 104541 Bolt\n10 1 90083-2 <PERSON><PERSON><PERSON>\n11 1 90088 Ring\n12 3 105112 Screw\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0)416 511 656, Telephone no. +46 (0)416 513 100", "page_range": "47-48", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 47, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 4, "merged": true}, "token_count": 398, "semantic_summary": "Spare parts breakdown pipe and flanges SCANJET SC 90 T2 RT List dated 2004-03-19. Spare parts number may be changed without prior notice. Final spare parts numbers will be issued for “shipset manual’."}, {"chunk_id": "chunk_0205", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_48_0\n\n| Spare parts breakdown pipe and flanges SCANJET SC 90 T2 RT List dated 2004-03-19\nNote! Spare parts number may be changed without prior notice.\nFinal spare parts numbers will be issued for “shipset manual”. |  |  |  |\n| --- | --- | --- | --- |\n| POS. NO | QTY. | SC PART NO. | DESCRIPTION |\n| 1 | 1 | 90366-xxx | Pipe |\n| 2 | 12 | 102552 | Nut |\n| 3 | 12 | 102639 | Washer |\n| 4 | 1 | 90090 | Sealing |\n| 5 | 8 | 120274 | Pin bolt |\n| 6 | 1 | 90082-xx | Flange acording to order see below |\n|  |  | 90082-10 | Ø480 / Ø435 No of holes 12 |\n|  |  | 90082-20 | Ø420 / Ø380 No of holes 8 |\n|  |  | 90082-21 | Ø430 / Ø390 No of holes 12 |\n|  |  | 90082-22 | Ø480 / Ø435 No of holes 8 |\n|  |  | 90082-23 | Ø540 / Ø495 No of holes 16 |\n| 7 | 1 | 109592 | O-Ring |\n| 8 | 1 | 90086-2 | Sealing |\n| 9 | 4 | 104541 | Bolt |\n| 10 | 1 | 90083-2 | Flange |\n| 11 | 1 | 90088 | Ring |\n| 12 | 3 | 105112 | Screw |", "page_range": "48", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 48, "table_id": "table_48_0", "table_rows": 19, "table_cols": 4}, "token_count": 380, "semantic_summary": "Tabular data with 19 rows"}, {"chunk_id": "chunk_0206", "title": "INSTRUCTION", "content": "INSTRUCTION\n\nMANUAL", "page_range": "49-49", "section_hierarchy": ["INSTRUCTION"], "chunk_type": "section", "metadata": {"page_number": 49, "section_path": "INSTRUCTION", "content_lines": 1, "merged": true}, "token_count": 5, "semantic_summary": "INSTRUCTION\n\nMANUAL"}, {"chunk_id": "chunk_0208", "title": "TRIPOD", "content": "TRIPOD\n#TRIPOD-03.doc\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "49", "section_hierarchy": ["TRIPOD"], "chunk_type": "section", "metadata": {"page_number": 49, "section_path": "TRIPOD", "content_lines": 4}, "token_count": 69, "semantic_summary": "TRIPOD\n#TRIPOD-03. doc\nSCANJET MARINE AB, P."}, {"chunk_id": "chunk_0209", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 2 of (10)\n#TRIPOD-03", "page_range": "50", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 50, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 14, "semantic_summary": "Page 2 of (10)\n#TRIPOD-03"}, {"chunk_id": "chunk_0210", "title": "CONTENTS", "content": "CONTENTS\n\nDESCRIPTION PAGE\nINTRODUCTION 3\nTRIPOD ASSEMBLY INSTRUCTION 4\nLIFTING INSTRUCTIONS 5\nHOW TO ORDER SPARE PARTS 6\nSPARE PART BREA<PERSON><PERSON>OWN TRIPOD 6 METER 7\nSPARE PART BREAK<PERSON>OW<PERSON> TRIPOD 3 METER 8\nSPARE PART BREAKDOWN TRIPOD 4 METER 9\nNOTES 10\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "50-50", "section_hierarchy": ["CONTENTS"], "chunk_type": "section", "metadata": {"page_number": 50, "section_path": "CONTENTS", "content_lines": 1, "merged": true}, "token_count": 146, "semantic_summary": "CONTENTS\n\nDESCRIPTION PAGE\nINTRODUCTION 3\nTRIPOD ASSEMBLY INSTRUCTION 4\nLIFTING INSTRUCTIONS 5\nHOW TO ORDER SPARE PARTS 6\nSPARE PART BREAK<PERSON>OWN TRIPOD 6 METER 7\nSPARE PART BREAK<PERSON>OWN TRIPOD 3 METER 8\nSPARE PART BREAKDOWN TRIPOD 4 METER 9\nNOTES 10\nSCANJET MARINE AB, P. O."}, {"chunk_id": "chunk_0212", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 3 of (10)\n#TRIPOD-03", "page_range": "51", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 51, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 14, "semantic_summary": "Page 3 of (10)\n#TRIPOD-03"}, {"chunk_id": "chunk_0213", "title": "INTRODUCTION", "content": "INTRODUCTION\nSCANJET TRIPOD is a lifting device specially developed for lifting up tankcleaning\nmachines for service onboard Crude, Chemical or Product-carriers.\nThere are three versions of the Tripod:\n\n1. Tripod 3 meters", "page_range": "51-51", "section_hierarchy": ["INTRODUCTION"], "chunk_type": "section", "metadata": {"page_number": 51, "section_path": "INTRODUCTION", "content_lines": 4, "merged": true}, "token_count": 52, "semantic_summary": "INTRODUCTION\nSCANJET TRIPOD is a lifting device specially developed for lifting up tankcleaning\nmachines for service onboard Crude, Chemical or Product-carriers. \nThere are three versions of the Tripod:\n\n1."}, {"chunk_id": "chunk_0215", "title": "2. <PERSON><PERSON> 4 meters", "content": "2. Tripod 4 meters\n\n3. Tripod 6 meters parted\nThe version delivered is depending of the length of the machines, which are to be lifted.\nThe Tripod consists of two main parts, the stand and a lifting block. The lifting block is\nmanually driven be means of pulling a chain, which drives a gear that pulls the lifting chain.\nThe lifting chain is supplied with a closable hook. The lifting block is allowed to lift up to 500\nkg.\nTripod version Length folded together Weight (kg)\n3 m 3,7 m 62\n4 m 4,7 m 70\n6 m (disassembled) 3,8 m 110\n6 m (assembled) 6,8 110\nIMPORTANT!\nNOTE! The use of the Tripod may cause static electricity why measures must be taken\nin order to avoid sparks and possible explosion by having the tanks inert at time of\nlifting and handling.\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "51-51", "section_hierarchy": ["2. <PERSON><PERSON> 4 meters"], "chunk_type": "section", "metadata": {"page_number": 51, "section_path": "2. <PERSON><PERSON> 4 meters", "content_lines": 1, "merged": true}, "token_count": 255, "semantic_summary": "The Tripod consists of two main parts, the stand and a lifting block. The lifting block is allowed to lift up to 500kg. The use of the Tripod may cause static electricity why measures must be taken to avoid sparks and possible explosion."}, {"chunk_id": "chunk_0217", "title": "Table from Document", "content": "Table ID: table_51_0\n\n| Tripod version | Length folded together | Weight (kg) |\n| --- | --- | --- |\n| 3 m | 3,7 m | 62 |\n| 4 m | 4,7 m | 70 |\n| 6 m (disassembled) | 3,8 m | 110 |\n| 6 m (assembled) | 6,8 | 110 |", "page_range": "51", "section_hierarchy": ["3. <PERSON><PERSON> 6 meters parted"], "chunk_type": "table", "metadata": {"page_number": 51, "table_id": "table_51_0", "table_rows": 5, "table_cols": 3}, "token_count": 94, "semantic_summary": "Tabular data with 5 rows"}, {"chunk_id": "chunk_0218", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 4 of (10)\n#TRIPOD-03", "page_range": "52", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 52, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 14, "semantic_summary": "Page 4 of (10)\n#TRIPOD-03"}, {"chunk_id": "chunk_0219", "title": "TRIPOD ASSEMBLY INSTRUCTION", "content": "TRIPOD ASSEMBLY INSTRUCTION\nThe tripod is delivered in two main parts the lifting block and the stand. The six-meter tripod\nis parted and has to be assembled.\n\n1. Make sure that the loops on the legs are pointing towards the centre.", "page_range": "52-52", "section_hierarchy": ["TRIPOD ASSEMBLY INSTRUCTION"], "chunk_type": "section", "metadata": {"page_number": 52, "section_path": "TRIPOD ASSEMBLY INSTRUCTION", "content_lines": 3, "merged": true}, "token_count": 54, "semantic_summary": "TRIPOD ASSEMBLY INSTRUCTION\nThe tripod is delivered in two main parts the lifting block and the stand.  The six-meter tripod\nis parted and has to be assembled."}, {"chunk_id": "chunk_0221", "title": "2. Secure the security-chain to the loops on the legs with shackles.", "content": "2. Secure the security-chain to the loops on the legs with shackles.\n\n3. Secure the lifting gear to the lifting eye with the security hook.", "page_range": "52-52", "section_hierarchy": ["2. Secure the security-chain to the loops on the legs with shackles."], "chunk_type": "section", "metadata": {"page_number": 52, "section_path": "2. Secure the security-chain to the loops on the legs with shackles.", "content_lines": 1, "merged": true}, "token_count": 31, "semantic_summary": "2.  Secure the security-chain to the loops on the legs with shackles."}, {"chunk_id": "chunk_0223", "title": "4. Make sure that all the nuts and bolts are tightened.", "content": "4. Make sure that all the nuts and bolts are tightened.\n\n1. PARTED TRIPOD", "page_range": "52-52", "section_hierarchy": ["4. Make sure that all the nuts and bolts are tightened."], "chunk_type": "section", "metadata": {"page_number": 52, "section_path": "4. Make sure that all the nuts and bolts are tightened.", "content_lines": 1, "merged": true}, "token_count": 20, "semantic_summary": "4. Make sure that all the nuts and bolts are tightened.\n\n1. PARTED TRIPOD"}, {"chunk_id": "chunk_0225", "title": "2. ASSEMBLED TRIPOD READY FOR LIFT 3. LIFTING UP MACHINE", "content": "2. ASSEMBLED TRIPOD READY FOR LIFT 3. LIFTING UP MACHINE\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "52", "section_hierarchy": ["2. ASSEMBLED TRIPOD READY FOR LIFT 3. LIFTING UP MACHINE"], "chunk_type": "section", "metadata": {"page_number": 52, "section_path": "2. ASSEMBLED TRIPOD READY FOR LIFT 3. LIFTING UP MACHINE", "content_lines": 3}, "token_count": 76, "semantic_summary": "2.  ASSEMBLED TRIPOD READY FOR LIFT 3."}, {"chunk_id": "chunk_0226", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 5 of (10)\n#TRIPOD-03", "page_range": "53", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 53, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 14, "semantic_summary": "Page 5 of (10)\n#TRIPOD-03"}, {"chunk_id": "chunk_0227", "title": "LIFTING INSTRUCTIONS", "content": "LIFTING INSTRUCTIONS\n\n1. Assemble the Tripod.", "page_range": "53-53", "section_hierarchy": ["LIFTING INSTRUCTIONS"], "chunk_type": "section", "metadata": {"page_number": 53, "section_path": "LIFTING INSTRUCTIONS", "content_lines": 1, "merged": true}, "token_count": 14, "semantic_summary": "LIFTING INSTRUCTIONS\n\n1. Assemble the Tripod."}, {"chunk_id": "chunk_0229", "title": "2. <PERSON><PERSON> the <PERSON>od.", "content": "2. Raise the Tripod.\n\n3. Place the Tripod over the machine, which is to be lifted. Make sure that the lifting\nwith the sea. If the deck is sloped always place one leg in the down-slope and the other\ntwo in the up-slope. If there is too much slope put something under the leg so that the\nmachine is levelled. This is very important so that the Tripod will not fall when lifting.", "page_range": "53-53", "section_hierarchy": ["2. <PERSON><PERSON> the <PERSON>od."], "chunk_type": "section", "metadata": {"page_number": 53, "section_path": "2. <PERSON><PERSON> the <PERSON>od.", "content_lines": 1, "merged": true}, "token_count": 94, "semantic_summary": "2.  <PERSON><PERSON> the <PERSON>od."}, {"chunk_id": "chunk_0231", "title": "4. Unscrew all nuts holding the machine, at the deck-flange and the inlet flange.", "content": "4. Unscrew all nuts holding the machine, at the deck-flange and the inlet flange.\n\n5. Secure the machine with a soft sling strap around the inlet-house (see picture below).", "page_range": "53-53", "section_hierarchy": ["4. Unscrew all nuts holding the machine, at the deck-flange and the inlet flange."], "chunk_type": "section", "metadata": {"page_number": 53, "section_path": "4. Unscrew all nuts holding the machine, at the deck-flange and the inlet flange.", "content_lines": 1, "merged": true}, "token_count": 40, "semantic_summary": "4.  Unscrew all nuts holding the machine, at the deck-flange and the inlet flange."}, {"chunk_id": "chunk_0233", "title": "6. Attach the lifting chain to the sling strap using the safety hook, make sure that it is", "content": "6. Attach the lifting chain to the sling strap using the safety hook, make sure that it is\nsecurely attached.\n\n7. Pull the chain attached to the lifting block and lift the machine. Make sure that\nmachine does not bump in to the deck-flange. Protect the nozzle and the bottom part\nof the machine from damage. Never hold the machine in the nozzle because this could\nchange the settings of the machine and eventually seriously damage it.", "page_range": "53-53", "section_hierarchy": ["6. Attach the lifting chain to the sling strap using the safety hook, make sure that it is"], "chunk_type": "section", "metadata": {"page_number": 53, "section_path": "6. Attach the lifting chain to the sling strap using the safety hook, make sure that it is", "content_lines": 2, "merged": true}, "token_count": 92, "semantic_summary": "6.  Attach the lifting chain to the sling strap using the safety hook, make sure that it is\nsecurely attached."}, {"chunk_id": "chunk_0235", "title": "8. To lower the machine, pull the other end of the chain.", "content": "8. To lower the machine, pull the other end of the chain.\n\nNote: Max lifting\nweight 500kg\nIMPORTANT!\nThe use of the Tripod may cause static electricity why measures must be taken in order\nto avoid sparks and possible explosion by having the tanks inert at time of lifting and\nhandling.\nNever stand under lifted machine!\nNever hold the machine in the nozzle!\nNever use the Tripod at rough sea.\nAlways use protection cap, protection gloves and protection shoes when operating\nTripod\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "53-53", "section_hierarchy": ["8. To lower the machine, pull the other end of the chain."], "chunk_type": "section", "metadata": {"page_number": 53, "section_path": "8. To lower the machine, pull the other end of the chain.", "content_lines": 1, "merged": true}, "token_count": 158, "semantic_summary": "The use of the Tripod may cause static electricity why measures must be taken in order to avoid sparks and possible explosion. To lower the machine, pull the other end of the chain."}, {"chunk_id": "chunk_0237", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 6 of (10)\n#TRIPOD-03\nHow to order spare parts\nTripod\nWhen ordering spare parts the following data must be referred to for securing a correct and\nrapid delivery.\nName of Vessel / Hull no:", "page_range": "54", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 54, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 7}, "token_count": 52, "semantic_summary": "Page 6 of (10)\n#TRIPOD-03\nHow to order spare parts\nTripod\nWhen ordering spare parts the following data must be referred to for securing a correct and\nrapid delivery. \nName of Vessel / Hull no:."}, {"chunk_id": "chunk_0238", "title": "Invoice address:", "content": "Invoice address:\n\nConsignee:", "page_range": "54-54", "section_hierarchy": ["Including: Jinling Shipyard", "Invoice address:"], "chunk_type": "section", "metadata": {"page_number": 54, "section_path": "Including: Jinling Shipyard > Invoice address:", "content_lines": 1, "merged": true}, "token_count": 7, "semantic_summary": "Invoice address:\n\nConsignee:"}, {"chunk_id": "chunk_0240", "title": "Your order no:", "content": "Your order no:\nContact Person:\n\nMode of delivery:\nLatest ETA destination:", "page_range": "54-54", "section_hierarchy": ["Including: Jinling Shipyard", "Your order no:"], "chunk_type": "section", "metadata": {"page_number": 54, "section_path": "Including: Jinling Shipyard > Your order no:", "content_lines": 2, "merged": true}, "token_count": 15, "semantic_summary": "Your order no:\nContact Person:\n\nMode of delivery:\nLatest ETA destination:"}, {"chunk_id": "chunk_0242", "title": "Shipping mark:", "content": "Shipping mark:\n\nType of equipment:", "page_range": "54-54", "section_hierarchy": ["Including: Jinling Shipyard", "Shipping mark:"], "chunk_type": "section", "metadata": {"page_number": 54, "section_path": "Including: Jinling Shipyard > Shipping mark:", "content_lines": 1, "merged": true}, "token_count": 7, "semantic_summary": "Shipping mark:\n\nType of equipment:"}, {"chunk_id": "chunk_0244", "title": "Spare part list:", "content": "Spare part list:\n\nPOS PART NO QTY DESCRIPTION\n_____________________________________________________________\n....... ................. ....... ...................................\n....... ................. ....... ...................................\n....... ................. ....... ...................................\n....... ................. ....... ...................................\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "54-54", "section_hierarchy": ["Including: Jinling Shipyard", "Spare part list:"], "chunk_type": "section", "metadata": {"page_number": 54, "section_path": "Including: Jinling Shipyard > Spare part list:", "content_lines": 1, "merged": true}, "token_count": 99, "semantic_summary": "SCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden. Phone no. +46 (0) 416 511 656."}, {"chunk_id": "chunk_0246", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 7 of (10)\n#TRIPOD-03\nSPARE PART BR<PERSON><PERSON><PERSON><PERSON><PERSON> TRIPOD 3 meter\nSPARE PARTS BREAKDOWN LIST DATED 2004-08-25\nSC PART NO. QTY DESCRIPTION\n95111-2 2 Pipe\n95111 1 Pipe Middle\n95114 3 Rubber foot\n95108 1 Shaft\n95109 1 Lifting Eye\n120440 1 Security Chain\n120441 1 Lifting Gear\nBolts + Shackle\n120339 3 Shackle\n105968 2 Locking Nut M16\n106112 2 Washer for M16\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "55", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 55, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 18}, "token_count": 191, "semantic_summary": "Page 7 of (10)\n#TRIPOD-03\nSPARE PART BR<PERSON><PERSON><PERSON><PERSON><PERSON> TRIPOD 3 meter\nSPARE PARTS BREAKDOWN LIST DATED 2004-08-25\nSC PART NO.  QTY DESCRIPTION\n95111-2 2 Pipe\n95111 1 Pipe Middle\n95114 3 Rubber foot\n95108 1 Shaft\n95109 1 Lifting Eye\n120440 1 Security Chain\n120441 1 Lifting Gear\nBolts + Shackle\n120339 3 Shackle\n105968 2 Locking Nut M16\n106112 2 Washer for M16\nSCANJET MARINE AB, P."}, {"chunk_id": "chunk_0247", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_55_0\n\n| SPARE PARTS BREAKDOWN LIST DATED 2004-08-25 |  |  |\n| --- | --- | --- |\n| SC PART NO. | QTY | DESCRIPTION |\n| 95111-2 | 2 | Pipe |\n| 95111 | 1 | Pipe Middle |\n| 95114 | 3 | Rubber foot |\n| 95108 | 1 | Shaft |\n| 95109 | 1 | Lifting Eye |\n| 120440 | 1 | Security Chain |\n| 120441 | 1 | Lifting Gear |\n| Bolts + Shackle |  |  |\n| 120339 | 3 | Shackle |\n| 105968 | 2 | Locking Nut M16 |\n| 106112 | 2 | Washer for M16 |\n|  |  |  |", "page_range": "55", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 55, "table_id": "table_55_0", "table_rows": 14, "table_cols": 3}, "token_count": 184, "semantic_summary": "Tabular data with 14 rows"}, {"chunk_id": "chunk_0248", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 8 of (10)\n#TRIPOD-03\nSPARE PART <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TRIPOD 4 meter\nSPARE PARTS BREAKDOWN LIST DATED 2004-08-25\nSC PART NO. QTY DESCRIPTION\n95112-2 2 Pipe\n95112 1 Pipe Middle\n95104 3 Stands\n95108 1 Shaft\n95109 1 Lifting Eye\n120440 1 Security Chain\n120441 1 Lifting Gear\nBolts + Shackle\n120339 3 Shackle\n105968 2 Locking Nut M16\n106112 2 Washer for M16\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "56", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 56, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 18}, "token_count": 191, "semantic_summary": "Page 8 of (10)\n#TRIPOD-03\nSPARE PART BR<PERSON><PERSON><PERSON><PERSON><PERSON> TRIPOD 4 meter\nSPARE PARTS BREAKDOWN LIST DATED 2004-08-25\nSC PART NO.  QTY DESCRIPTION\n95112-2 2 Pipe\n95112 1 Pipe Middle\n95104 3 Stands\n95108 1 Shaft\n95109 1 Lifting Eye\n120440 1 Security Chain\n120441 1 Lifting Gear\nBolts + Shackle\n120339 3 Shackle\n105968 2 Locking Nut M16\n106112 2 Washer for M16\nSCANJET MARINE AB, P."}, {"chunk_id": "chunk_0249", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_56_0\n\n| SPARE PARTS BREAKDOWN LIST DATED 2004-08-25 |  |  |\n| --- | --- | --- |\n| SC PART NO. | QTY | DESCRIPTION |\n| 95112-2 | 2 | Pipe |\n| 95112 | 1 | Pipe Middle |\n| 95104 | 3 | Stands |\n| 95108 | 1 | Shaft |\n| 95109 | 1 | Lifting Eye |\n| 120440 | 1 | Security Chain |\n| 120441 | 1 | Lifting Gear |\n| Bolts + Shackle |  |  |\n| 120339 | 3 | Shackle |\n| 105968 | 2 | Locking Nut M16 |\n| 106112 | 2 | Washer for M16 |\n|  |  |  |", "page_range": "56", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 56, "table_id": "table_56_0", "table_rows": 14, "table_cols": 3}, "token_count": 184, "semantic_summary": "Tabular data with 14 rows"}, {"chunk_id": "chunk_0250", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 9 of (10)\n#TRIPOD-03\nSPARE PART BREA<PERSON><PERSON><PERSON>N TRIPOD 6 meters parted\nSPARE PARTS BREAKDOWN LIST DATED 2004-08-25\nSC PART NO. QTY DESCRIPTION\n95101 3 Lower pipe\n95102-2 2 Top Pipe\n95102 1 Top Pipe Middle\n95103 3 Middle Pipe\n95114 3 R<PERSON>ber foot\n95108 1 Shaft\n95109 1 Lifting Eye\n120440 1 Security Chain\n120441 1 Lifting Gear\nBolts + Shackle\n120339 3 Shackle\n104513 6 Bolt M12\n105967 6 Locking Nut M12\n105968 2 Locking Nut M16\n106112 2 Washer For M16\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "57", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 57, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 22}, "token_count": 226, "semantic_summary": "SCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden. Phone no. +46 (0) 416 511 656."}, {"chunk_id": "chunk_0251", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_57_0\n\n| SPARE PARTS BREAKDOWN LIST DATED 2004-08-25 |  |  |\n| --- | --- | --- |\n| SC PART NO. | QTY | DESCRIPTION |\n| 95101 | 3 | Lower pipe |\n| 95102-2 | 2 | Top Pipe |\n| 95102 | 1 | Top Pipe Middle |\n| 95103 | 3 | Middle Pipe |\n| 95114 | 3 | Rubber foot |\n| 95108 | 1 | Shaft |\n| 95109 | 1 | Lifting Eye |\n| 120440 | 1 | Security Chain |\n| 120441 | 1 | Lifting Gear |\n| Bolts + Shackle |  |  |\n| 120339 | 3 | Shackle |\n| 104513 | 6 | Bolt M12 |\n| 105967 | 6 | Locking Nut M12 |\n| 105968 | 2 | Locking Nut M16 |\n| 106112 | 2 | Washer For M16 |\n|  |  |  |\n|  |  |  |", "page_range": "57", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 57, "table_id": "table_57_0", "table_rows": 19, "table_cols": 3}, "token_count": 241, "semantic_summary": "Tabular data with 19 rows"}, {"chunk_id": "chunk_0252", "title": "Tel: + 46 31 338 7530: Page", "content": "Page 10 of (10)\n#TRIPOD-03", "page_range": "58", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "text", "metadata": {"page_number": 58, "section_path": "Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content_lines": 2}, "token_count": 14, "semantic_summary": "Page 10 of (10)\n#TRIPOD-03"}, {"chunk_id": "chunk_0253", "title": "NOTES", "content": "NOTES\nPOS. NO SC PART NO. Remarks\nSCANJET MARINE AB, P.O. Box 2, S-275 21 SJÖBO, Sweden\nTelefax no. +46 (0) 416 511 656, Telephone no. +46 (0) 416 513 100", "page_range": "58", "section_hierarchy": ["NOTES"], "chunk_type": "section", "metadata": {"page_number": 58, "section_path": "NOTES", "content_lines": 4}, "token_count": 67, "semantic_summary": "NOTES\nPOS.  NO SC PART NO."}, {"chunk_id": "chunk_0254", "title": "Table from Document", "content": "Table ID: table_58_0\n\n| NOTES |  |  |\n| --- | --- | --- |\n| POS. NO | SC PART NO. | Remarks |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n|  |  |  |\n\nTable ID: table_59_0\n\n|  |  |  |\n| --- | --- | --- |\n|  |  |  |", "page_range": "58-59", "section_hierarchy": ["NOTES"], "chunk_type": "table", "metadata": {"page_number": 58, "table_id": "table_58_0", "table_rows": 24, "table_cols": 3, "merged": true}, "token_count": 219, "semantic_summary": "Table ID: table_58_0\n\n| NOTES |  |  |\n| --- | --- | --- |\n| POS.  NO | SC PART NO."}, {"chunk_id": "chunk_0256", "title": "Table from Including: Jinling Shipyard > Tel: + 46 31 338 7530", "content": "Table ID: table_59_1\n\n|  |  |\n| --- | --- |\n|  |  |\n|  |  |\n|  |  |\n|  |  |\n|  |  |\n|  |  |\n\nTable ID: table_59_2\n\n|  |  |  |  |\n| --- | --- | --- | --- |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |", "page_range": "59-59", "section_hierarchy": ["Including: Jinling Shipyard", "Tel: + 46 31 338 7530"], "chunk_type": "table", "metadata": {"page_number": 59, "table_id": "table_59_1", "table_rows": 7, "table_cols": 2, "merged": true}, "token_count": 139, "semantic_summary": "Table ID: table_59_1\n\n|  |  |\n| --- | --- |\n|  |  |\n|  |  |\n|  |  |\n|  |  |\n|  |  |\n|  |  |\n\nTable ID: table_59_2\n\n|  |  |  |  |\n| --- | --- | --- | --- |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |\n|  |  |  |  |."}]